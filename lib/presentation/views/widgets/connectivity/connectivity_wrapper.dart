import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/presentation/views/features/connectivity/connectivity_provider.dart';
import 'package:vcc/presentation/views/widgets/dialogs/connectivity_dialog.dart';

class ConnectivityWrapper extends ConsumerStatefulWidget {
  final Widget child;

  const ConnectivityWrapper({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<ConnectivityWrapper> createState() => _ConnectivityWrapperState();
}

class _ConnectivityWrapperState extends ConsumerState<ConnectivityWrapper> {
  bool _isDialogShowing = false;

  @override
  Widget build(BuildContext context) {
    ref.listen<ConnectivityState>(
      connectivityProvider,
      (previous, current) {
        if (kDebugMode) {
          print('ConnectivityWrapper: showDialog=${current.showDialog}, _isDialogShowing=$_isDialogShowing, isConnected=${current.isConnected}');
        }

        // Show dialog when connection is lost
        if (current.showDialog && !_isDialogShowing) {
          if (kDebugMode) {
            print('ConnectivityWrapper: Showing connectivity dialog');
          }
          _isDialogShowing = true;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              ConnectivityDialog.show(
                context,
                onRetry: () async {
                  if (kDebugMode) {
                    print('ConnectivityWrapper: Retry button pressed');
                  }
                  final navigator = Navigator.of(context);
                  final isConnected = await ref
                      .read(connectivityProvider.notifier)
                      .checkConnectivity();

                  if (kDebugMode) {
                    print('ConnectivityWrapper: Retry result - isConnected: $isConnected');
                  }

                  if (isConnected && mounted) {
                    navigator.pop();
                    _isDialogShowing = false;
                    ref.read(connectivityProvider.notifier).hideDialog();
                  }
                },
              );
            }
          });
        }

        // Hide dialog when connection is restored
        if (!current.showDialog && _isDialogShowing) {
          if (kDebugMode) {
            print('ConnectivityWrapper: Hiding connectivity dialog');
          }
          _isDialogShowing = false;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            }
          });
        }
      },
    );

    return widget.child;
  }
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/presentation/views/features/connectivity/connectivity_provider.dart';
import 'package:vcc/presentation/views/widgets/connectivity/connectivity_wrapper.dart';

class ConnectivityInitializer extends ConsumerStatefulWidget {
  final Widget child;

  const ConnectivityInitializer({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<ConnectivityInitializer> createState() => _ConnectivityInitializerState();
}

class _ConnectivityInitializerState extends ConsumerState<ConnectivityInitializer> {
  @override
  void initState() {
    super.initState();
    // Initialize connectivity provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // This will trigger the provider initialization
      ref.read(connectivityProvider);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      child: widget.child,
    );
  }
}

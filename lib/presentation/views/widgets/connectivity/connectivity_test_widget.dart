import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/presentation/views/features/connectivity/connectivity_provider.dart';
import 'package:vcc/presentation/views/widgets/dialogs/connectivity_dialog.dart';

class ConnectivityTestWidget extends ConsumerWidget {
  const ConnectivityTestWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivityState = ref.watch(connectivityProvider);
    
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border.all(color: Colors.blue),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Connectivity Test Widget',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text('Connection Status: ${connectivityState.connectivityResult}'),
          Text('Is Connected: ${connectivityState.isConnected}'),
          Text('Show Dialog: ${connectivityState.showDialog}'),
          const SizedBox(height: 12),
          Row(
            children: [
              ElevatedButton(
                onPressed: () async {
                  final isConnected = await ref
                      .read(connectivityProvider.notifier)
                      .checkConnectivity();
                  if (kDebugMode) {
                    print('Manual check result: $isConnected');
                  }
                },
                child: const Text('Check Connectivity'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {
                  ConnectivityDialog.show(
                    context,
                    onRetry: () async {
                      final isConnected = await ref
                          .read(connectivityProvider.notifier)
                          .checkConnectivity();
                      if (kDebugMode) {
                        print('Dialog retry result: $isConnected');
                      }
                      if (isConnected) {
                        Navigator.of(context).pop();
                      }
                    },
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

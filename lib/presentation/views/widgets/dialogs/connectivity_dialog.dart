import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:vcc/generated/assets.gen.dart';

class ConnectivityDialog extends StatelessWidget {
  final VoidCallback? onRetry;

  const ConnectivityDialog({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 24),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: BaseColors.backgroundWhite,
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            MyAssets.icons.iconError.svg(
              width: 64,
              height: 64,
              colorFilter: ColorFilter.mode(
                BaseColors.error,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(height: 16),

            // Title
            Text(
              '<PERSON>h<PERSON>ng có kết nối mạng',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: BaseColors.textTitle,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // Message
            Text(
              'Vui lòng kiểm tra kết nối mạng và thử lại',
              style: TextStyle(
                fontSize: 16,
                color: BaseColors.textSubtitle,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Retry Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: BaseColors.primary,
                  foregroundColor: BaseColors.backgroundWhite,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Thử lại',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void show(
    BuildContext context, {
    VoidCallback? onRetry,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ConnectivityDialog(
          onRetry: onRetry,
        );
      },
    );
  }
}

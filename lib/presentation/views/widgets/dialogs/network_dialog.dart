import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:vcc/generated/assets.gen.dart';

class NetworkDialog extends StatelessWidget {
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;

  const NetworkDialog({
    super.key,
    this.onRetry,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent back button dismiss
      child: Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 24),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: BaseColors.backgroundWhite,
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon
              MyAssets.icons.iconWarning.svg(
                width: 64,
                height: 64,
                colorFilter: ColorFilter.mode(
                  BaseColors.warning,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(height: 16),
              
              // Title
              Text(
                'Không có kết nối mạng',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: BaseColors.textTitle,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              
              // Message
              Text(
                'Vui lòng kiểm tra kết nối mạng và thử lại',
                style: TextStyle(
                  fontSize: 14,
                  color: BaseColors.textSubtitle,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              
              // Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        onDismiss?.call();
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: BaseColors.primary),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Đóng',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: BaseColors.primary,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: onRetry,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: BaseColors.primary,
                        foregroundColor: BaseColors.backgroundWhite,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Thử lại',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  static void show(
    BuildContext context, {
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return NetworkDialog(
          onRetry: onRetry,
          onDismiss: onDismiss,
        );
      },
    );
  }
}

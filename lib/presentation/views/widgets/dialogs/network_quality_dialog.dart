import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/utils/network_quality_monitor.dart';

class NetworkQualityDialog extends StatefulWidget {
  final NetworkQualityResult result;
  final Future<NetworkQualityResult> Function()? onRetry;
  final VoidCallback? onDismiss;

  const NetworkQualityDialog({
    super.key,
    required this.result,
    this.onRetry,
    this.onDismiss,
  });

  @override
  State<NetworkQualityDialog> createState() => _NetworkQualityDialogState();
}

class _NetworkQualityDialogState extends State<NetworkQualityDialog> {
  bool _isRetrying = false;
  NetworkQualityResult? _currentResult;

  @override
  void initState() {
    super.initState();
    _currentResult = widget.result;
  }

  Color _getQualityColor(NetworkQuality quality) {
    switch (quality) {
      case NetworkQuality.excellent:
        return Colors.green;
      case NetworkQuality.good:
        return Colors.lightGreen;
      case NetworkQuality.fair:
        return Colors.orange;
      case NetworkQuality.poor:
        return Colors.deepOrange;
      case NetworkQuality.bad:
      case NetworkQuality.none:
        return BaseColors.error;
    }
  }

  Widget _getQualityIcon(NetworkQuality quality) {
    switch (quality) {
      case NetworkQuality.excellent:
      case NetworkQuality.good:
        return MyAssets.icons.iconSuccess.svg(
          width: 64,
          height: 64,
          colorFilter: const ColorFilter.mode(
            Colors.green,
            BlendMode.srcIn,
          ),
        );
      case NetworkQuality.fair:
        return MyAssets.icons.iconWarning.svg(
          width: 64,
          height: 64,
          colorFilter: const ColorFilter.mode(
            Colors.orange,
            BlendMode.srcIn,
          ),
        );
      case NetworkQuality.poor:
      case NetworkQuality.bad:
      case NetworkQuality.none:
        return MyAssets.icons.iconError.svg(
          width: 64,
          height: 64,
          colorFilter: ColorFilter.mode(
            BaseColors.error,
            BlendMode.srcIn,
          ),
        );
    }
  }

  String _getTitle(NetworkQuality quality) {
    switch (quality) {
      case NetworkQuality.excellent:
      case NetworkQuality.good:
        return 'Mạng tốt';
      case NetworkQuality.fair:
        return 'Mạng khá';
      case NetworkQuality.poor:
        return 'Mạng yếu';
      case NetworkQuality.bad:
        return 'Mạng rất yếu';
      case NetworkQuality.none:
        return 'Không có mạng';
    }
  }

  String _getDescription(NetworkQualityResult result) {
    switch (result.quality) {
      case NetworkQuality.excellent:
      case NetworkQuality.good:
        return 'Kết nối mạng ổn định, bạn có thể sử dụng bình thường.';
      case NetworkQuality.fair:
        return 'Mạng hơi chậm nhưng vẫn có thể sử dụng được.';
      case NetworkQuality.poor:
        return 'Mạng yếu có thể ảnh hưởng đến trải nghiệm sử dụng. Khuyến nghị kiểm tra kết nối WiFi hoặc di chuyển đến vùng có sóng tốt hơn.';
      case NetworkQuality.bad:
        return 'Mạng rất yếu, có thể gây gián đoạn. Vui lòng kiểm tra kết nối WiFi, khởi động lại router hoặc liên hệ nhà cung cấp dịch vụ.';
      case NetworkQuality.none:
        return 'Không có kết nối mạng. Vui lòng kiểm tra WiFi hoặc dữ liệu di động.';
    }
  }

  @override
  Widget build(BuildContext context) {
    final result = _currentResult ?? widget.result;

    return PopScope(
      canPop: false,
      child: Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 24),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: BaseColors.backgroundWhite,
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon
              _getQualityIcon(result.quality),
              const SizedBox(height: 16),

              // Title
              Text(
                _getTitle(result.quality),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _getQualityColor(result.quality),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              // Quality details
              if (result.quality != NetworkQuality.none) ...[
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: BaseColors.backgroundGray,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Column(
                        children: [
                          Text(
                            '${result.averageLatency}ms',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: _getQualityColor(result.quality),
                            ),
                          ),
                          const Text(
                            'Độ trễ',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      Container(
                        width: 1,
                        height: 30,
                        color: Colors.grey.shade300,
                      ),
                      Column(
                        children: [
                          Text(
                            '${result.successRate.toStringAsFixed(0)}%',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: _getQualityColor(result.quality),
                            ),
                          ),
                          const Text(
                            'Thành công',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
              ],

              // Description
              Text(
                _getDescription(result),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: widget.onDismiss != null
                          ? () {
                              Navigator.of(context).pop();
                              widget.onDismiss!();
                            }
                          : null,
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: BaseColors.primary),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Đóng',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: BaseColors.primary,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isRetrying
                          ? null
                          : () async {
                              if (widget.onRetry != null) {
                                setState(() {
                                  _isRetrying = true;
                                });

                                try {
                                  final newResult = await widget.onRetry!();
                                  if (mounted) {
                                    setState(() {
                                      _currentResult = newResult;
                                      _isRetrying = false;
                                    });
                                  }
                                } catch (e) {
                                  if (mounted) {
                                    setState(() {
                                      _isRetrying = false;
                                    });
                                  }
                                }
                              }
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: BaseColors.primary,
                        foregroundColor: BaseColors.backgroundWhite,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isRetrying
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : const Text(
                              'Kiểm tra lại',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

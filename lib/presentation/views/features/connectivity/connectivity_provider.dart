import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/services/connectivity_service.dart';

// State class for connectivity
class ConnectivityState {
  final ConnectivityResult connectivityResult;
  final bool isConnected;
  final bool showDialog;

  ConnectivityState({
    required this.connectivityResult,
    required this.isConnected,
    this.showDialog = false,
  });

  ConnectivityState copyWith({
    ConnectivityResult? connectivityResult,
    bool? isConnected,
    bool? showDialog,
  }) {
    return ConnectivityState(
      connectivityResult: connectivityResult ?? this.connectivityResult,
      isConnected: isConnected ?? this.isConnected,
      showDialog: showDialog ?? this.showDialog,
    );
  }
}

// Notifier class
class ConnectivityNotifier extends StateNotifier<ConnectivityState> {
  ConnectivityNotifier() : super(ConnectivityState(
    connectivityResult: ConnectivityResult.none,
    isConnected: false,
  )) {
    _initialize();
  }

  final ConnectivityService _connectivityService = ConnectivityService();
  StreamSubscription<ConnectivityResult>? _subscription;

  void _initialize() async {
    await _connectivityService.initialize();
    
    // Set initial state
    final currentConnectivity = _connectivityService.currentConnectivity;
    state = state.copyWith(
      connectivityResult: currentConnectivity,
      isConnected: currentConnectivity != ConnectivityResult.none,
    );

    // Listen to connectivity changes
    _subscription = _connectivityService.connectivityStream.listen(
      (ConnectivityResult result) {
        final wasConnected = state.isConnected;
        final isNowConnected = result != ConnectivityResult.none;

        if (kDebugMode) {
          print('ConnectivityProvider: $result, wasConnected: $wasConnected, isNowConnected: $isNowConnected');
        }

        // Update state
        state = state.copyWith(
          connectivityResult: result,
          isConnected: isNowConnected,
          showDialog: !isNowConnected && !kDebugMode, // Show dialog only in release mode
        );

        // If connection restored, hide dialog
        if (wasConnected == false && isNowConnected) {
          hideDialog();
        }
      },
    );
  }

  void hideDialog() {
    state = state.copyWith(showDialog: false);
  }

  Future<bool> checkConnectivity() async {
    return await _connectivityService.checkConnectivity();
  }

  @override
  void dispose() {
    _subscription?.cancel();
    _connectivityService.dispose();
    super.dispose();
  }
}

// Provider
final connectivityProvider = StateNotifierProvider<ConnectivityNotifier, ConnectivityState>(
  (ref) => ConnectivityNotifier(),
);

import 'package:base_ui/base_ui.dart';
import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vcc/app_configs/coordinator.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/local/secure_storage.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_address/select_address_view.dart';
import 'package:vcc/presentation/views/features/agency/store_agency/store_agency_page.dart';
import 'package:vcc/presentation/views/features/agency/work_order_agency/work_agency_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_create/aio_contract_create_page.dart';
import 'package:vcc/presentation/views/features/home/<USER>';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/create_service_order_page.dart';
import 'package:vcc/presentation/views/features/profile/profile_page.dart';
import 'package:vcc/presentation/views/features/root/root_view_model.dart';
import 'package:vcc/presentation/views/features/store/store_page.dart';
import 'package:vcc/presentation/views/features/work/work_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/utils/network_monitor.dart';

class RootArguments {
  final int pageIndex;
  final bool? isAgency;

  RootArguments({
    required this.pageIndex,
    this.isAgency,
  });
}

class RootPage extends StatefulHookConsumerWidget {
  final RootArguments? arguments;

  const RootPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<RootPage> createState() => _RootPageState();
}

class _RootPageState extends ConsumerState<RootPage>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  late PageController pageController;
  TabController? _tabController;
  final now = DateTime.now();
  bool? isAgency = false;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    isAgency = widget.arguments?.isAgency ?? false;

    _tabController = TabController(
      length: (isAgency ?? false) ? 4 : 5,
      initialIndex: 0,
      vsync: this,
    );
    pageController = PageController(
      initialPage: widget.arguments?.pageIndex ?? 0,
      keepPage: true,
    );

    Future(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(rootProvider.notifier).changePageIndex(
              widget.arguments?.pageIndex ?? 0,
            );
        pageController.jumpToPage(
          widget.arguments?.pageIndex ?? 0,
        );
        _tabController?.animateTo(
          widget.arguments?.pageIndex ?? 0,
        );
      });
    });
    _checkFirstOpen();

    // Initialize network monitoring
    WidgetsBinding.instance.addPostFrameCallback((_) {
      NetworkMonitor().initialize(context);
    });

    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _tabController?.dispose();
    pageController.dispose();
    NetworkMonitor().dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    DateTime? currentBackPressTime;
    var canPop = ref.watch(rootProvider).currentIndex == 0;
    var state = ref.watch(rootProvider);

    return PopScope(
      canPop: canPop,
      onPopInvoked: (didPop) {
        if (didPop) {
          DateTime now = DateTime.now();
          if (currentBackPressTime == null ||
              now.difference(currentBackPressTime!) >
                  const Duration(seconds: 2)) {
            currentBackPressTime = now;
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Bấm lần nữa để thoát'),
              ),
            );
          }
        } else {
          ref.read(rootProvider.notifier).changePageIndex(0);
          pageController.jumpToPage(0);
          _tabController?.animateTo(0);
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: PageView(
          controller: pageController,
          physics: const NeverScrollableScrollPhysics(),
          children: (isAgency ?? false)
              ? childrenPageView()
              : <Widget>[
                  const HomePage(),
                  const StorePage(),
                  const SizedBox(),
                  const WorkPage(),
                  ProfilePage(
                    arguments: ProfileArguments(
                      onLogout: () {
                        ref.read(rootProvider.notifier).changePageIndex(1);
                        pageController.jumpToPage(1);
                        _tabController?.animateTo(1);
                        context.push(RouterPaths.login);
                      },
                    ),
                  ),
                ],
        ),
        bottomNavigationBar: ConvexAppBar(
          style: (isAgency ?? false) ? TabStyle.react : TabStyle.fixedCircle,
          initialActiveIndex: state.currentIndex,
          backgroundColor: Colors.white,
          color: BaseColors.textSubtitle,
          activeColor: BaseColors.primary,
          elevation: 0,
          controller: _tabController,
          items: (isAgency ?? false)
              ? bottomNavigationBarItems()
              : [
                  TabItem(
                    icon: buildIconNavigation(
                      child: MyAssets.icons.iconHomeInactivate.svg(),
                    ),
                    activeIcon: buildIconNavigation(
                      child: MyAssets.icons.iconHomeActivate.svg(),
                    ),
                    title: "Trang chủ",
                  ),
                  TabItem(
                    icon: buildIconNavigation(
                      child: MyAssets.icons.iconStoreInactivate.svg(),
                    ),
                    activeIcon: buildIconNavigation(
                      child: MyAssets.icons.iconStoreActivate.svg(),
                    ),
                    title: "Cửa hàng",
                  ),
                  TabItem(
                    icon: MyAssets.icons.iconCenterCircle.svg(),
                    activeIcon: MyAssets.icons.iconCenterCircle.svg(),
                  ),
                  TabItem(
                    icon: buildIconNavigation(
                      child: MyAssets.icons.iconCheckListInactivate.svg(),
                    ),
                    activeIcon: buildIconNavigation(
                      child: MyAssets.icons.iconCheckListActivate.svg(),
                    ),
                    title: "Công việc",
                  ),
                  TabItem(
                    icon: buildIconNavigation(
                      child: MyAssets.icons.iconPersonalInactivate.svg(),
                    ),
                    activeIcon: buildIconNavigation(
                      child: MyAssets.icons.iconPersonalActivate.svg(),
                    ),
                    title: "Menu",
                  ),
                ],
          onTap: (index) async {
            final isLogin = GlobalData.instance.userInfo != null;
            if (!isLogin) {
              if (index != 1) {
                context.push(RouterPaths.login);
                _tabController?.animateTo(1);
              } else {
                ref.read(rootProvider.notifier).changePageIndex(index);
                pageController.jumpToPage(index);
                _tabController?.animateTo(index);
              }
            } else {
              if (index == 2 && !(isAgency ?? true)) {
                await checkAddress(context);
              } else if (index != ref.watch(rootProvider).currentIndex) {
                ref.read(rootProvider.notifier).changePageIndex(index);
                pageController.jumpToPage(index);
                _tabController?.animateTo(index);
              }
            }
          },
        ),
      ),
    );
  }

  Future<void> checkAddress(BuildContext context) async {
    final result = await ref.read(rootProvider.notifier).getAddressFromIsar();

    if (!context.mounted) return;
    if (!result) {
      AppBottomSheet.showNormalBottomSheet(
        context,
        title: "Chọn địa chỉ",
        enableDrag: false,
        isDismissible: false,
        height: MediaQuery.of(context).size.height * 0.95,
        rightButton: const SizedBox(),
        child: SelectAddressView(
          onSelectAddress: (address) {
            ref.read(rootProvider.notifier).selectAddress(address);
          },
        ),
      );
    } else {
      var state = ref.watch(rootProvider);
      await showPopupMenu(context);
      pageController.jumpToPage(state.currentIndex);
      _tabController?.animateTo(state.currentIndex);
    }
  }

  Widget buildIconNavigation({
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.only(top: 4),
      child: child,
    );
  }

  Future<void> showPopupMenu(BuildContext context) async {
    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                width: 280,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    ListTile(
                      leading: MyAssets.icons.icWorkOrderPackage.svg(),
                      title: const Text('Tạo đơn gói giá'),
                      onTap: () {
                        context.pop();
                        context.push(
                          RouterPaths.aioContractCreate,
                          extra: AioContractCreateArguments(
                            data: "",
                          ),
                        );
                      },
                    ),
                    DividerWidget(
                      height: 1,
                      color: BaseColors.borderDefault,
                    ),
                    ListTile(
                      leading: MyAssets.icons.homeOrderService.svg(),
                      title: const Text('Tạo đơn dịch vụ'),
                      onTap: () {
                        context.pop();
                        context.push(
                          RouterPaths.createServiceOrder,
                          extra: CreateServiceOrderArguments(
                            isCreateSpeedOrder: true,
                            speedOrderType: ServiceType.single,
                          ),
                        );
                      },
                    ),
                    DividerWidget(
                      height: 1,
                      color: BaseColors.borderDefault,
                    ),
                    ListTile(
                      leading: MyAssets.icons.homeOrderProduct.svg(),
                      title: const Text('Tạo đơn sản phẩm'),
                      onTap: () {
                        context.pop();
                        context.push(
                          RouterPaths.createProductOrder,
                        );
                      },
                    ),
                    DividerWidget(
                      height: 1,
                      color: BaseColors.borderDefault,
                    ),
                    ListTile(
                      leading: MyAssets.icons.homeOrderSupplies.svg(),
                      title: const Text('Tạo đơn vật tư'),
                      onTap: () {
                        Navigator.pop(context);
                        context.push(
                          RouterPaths.createServiceOrder,
                          extra: CreateServiceOrderArguments(
                            isCreateSpeedOrder: true,
                            speedOrderType: ServiceType.supply,
                          ),
                        );
                      },
                    ),
                    DividerWidget(
                      height: 1,
                      color: BaseColors.borderDefault,
                    ),
                    if (GlobalData.instance.userInfo?.canCreateSalePointOrder ??
                        false) ...[
                      ListTile(
                        leading: MyAssets.icons.homeOrderSupplies.svg(),
                        title: const Text('Tạo phiếu mua hàng'),
                        onTap: () {
                          Navigator.pop(context);
                          context.push(
                            RouterPaths.createServiceOrder,
                            extra: CreateServiceOrderArguments(
                              isCreateSpeedOrder: true,
                              speedOrderType: ServiceType.salePoint,
                            ),
                          );
                        },
                      ),
                      DividerWidget(
                        height: 1,
                        color: BaseColors.borderDefault,
                      ),
                    ],
                    ListTile(
                      leading: MyAssets.icons.homeContactRequired.svg(),
                      title: const Text('Tạo yêu cầu tiếp xúc'),
                      onTap: () {
                        Navigator.pop(context);
                        context.push(
                          RouterPaths.createAssociation,
                        );
                      },
                    ),
                  ],
                ),
              ),
              MyAssets.images.shapeButton.image(
                height: 20,
              ),
              const SizedBox(
                height: 10,
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: MyAssets.icons.homeClose.svg(
                  height: 65,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _checkFirstOpen() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isFirstOpen = prefs.getBool('isFirstOpen') ?? true;

    String currentVersion = "0.0.2";
    String? storedVersion = prefs.getString('app_version');
    if (storedVersion != currentVersion) {
      await prefs.setString('app_version', currentVersion);
      _showPopup();
    }

    if (isFirstOpen) {
      prefs.setBool('isFirstOpen', false);
    }
  }

  void _showPopup() {
    var state = ref.watch(rootProvider);
    if ((state.versions ?? []).isNotEmpty) {
      AppBottomSheet.showNormalBottomSheet(
        context,
        title: "Tính năng mới",
        height: MediaQuery.of(context).size.height * 0.9,
        child: ListView.builder(
          itemCount: (state.versions ?? []).length,
          itemBuilder: (item, index) {
            var ver = (state.versions ?? [])[index];
            return Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(ver.name ?? ""),
                  const SizedBox(
                    height: 16,
                  ),
                  DividerWidget(
                    height: 1,
                    color: BaseColors.borderDefault,
                  ),
                ],
              ),
            );
          },
        ),
      );
    }
  }





  List<Widget> childrenPageView() {
    return [
      const HomePage(),
      const StoreAgencyPage(),
      const WorkAgencyPage(),
      ProfilePage(
        arguments: ProfileArguments(
          onLogout: () {
            _forceSignIn();
          },
        ),
      ),
    ];
  }

  List<TabItem> bottomNavigationBarItems() {
    return [
      TabItem(
        icon: buildIconNavigation(
          child: MyAssets.icons.iconHomeInactivate.svg(),
        ),
        activeIcon: buildIconNavigation(
          child: MyAssets.icons.iconHomeActivate.svg(),
        ),
        title: "Trang chủ",
      ),
      TabItem(
        icon: buildIconNavigation(
          child: MyAssets.icons.iconStoreInactivate.svg(),
        ),
        activeIcon: buildIconNavigation(
          child: MyAssets.icons.iconStoreActivate.svg(),
        ),
        title: "Cửa hàng",
      ),
      TabItem(
        icon: buildIconNavigation(
          child: MyAssets.icons.iconNoteBook.svg(),
        ),
        activeIcon: buildIconNavigation(
          child: MyAssets.icons.iconNoteBookS24.svg(
            colorFilter: ColorFilter.mode(
              BaseColors.primary,
              BlendMode.srcIn,
            ),
          ),
        ),
        title: "Công việc",
      ),
      TabItem(
        icon: buildIconNavigation(
          child: MyAssets.icons.iconPersonalInactivate.svg(),
        ),
        activeIcon: buildIconNavigation(
          child: MyAssets.icons.iconPersonalActivate.svg(),
        ),
        title: "Menu",
      ),
    ];
  }

  void _forceSignIn() {
    SecureStorage.instance.removeToken();
    SecureStorage.instance.removeUserName();
    GlobalData.instance.userInfo = null;

    while (AppCoordinator.context.canPop()) {
      AppCoordinator.context.pop();
    }
    AppCoordinator.context.go(RouterPaths.login);
  }
}

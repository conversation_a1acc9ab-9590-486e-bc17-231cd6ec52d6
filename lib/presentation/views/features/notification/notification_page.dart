import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/notification_category.dart';
import 'package:vcc/domain/enums/snack_bar_status.dart';
import 'package:vcc/presentation/base/app_snack_bar.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/notification/detail_notification/detail_notification_page.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/views/features/notification/widget/notification_item_widget.dart';
import 'package:vcc/presentation/views/features/notification/widgets/notification_filter_bottom_sheet.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/debouncer.dart';

import 'notification_view_model.dart';

class NotificationPage extends StatefulHookConsumerWidget {
  final Function? onNextPageCallBack;

  const NotificationPage({
    super.key,
    this.onNextPageCallBack,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _NotificationPage();
}

class _NotificationPage extends ConsumerState<NotificationPage> {
  late ScrollController scrollController;
  late TextEditingController searchController;
  late Debounce<String> debounce;

  @override
  void initState() {
    scrollController = ScrollController();
    searchController = TextEditingController();
    scrollController.addListener(_scrollListener);

    debounce = Debounce<String>(
      const Duration(milliseconds: 500),
      (value) {
        ref
            .read(notificationProvider.notifier)
            .searchNotifications(value ?? '');
      },
    );

    Future(() {
      ref.read(notificationProvider.notifier).loadAllData();
    });
    super.initState();
  }

  Future<void> refreshData() async {
    await ref.read(notificationProvider.notifier).loadUnreadCounts();
    await ref.read(notificationProvider.notifier).refreshData();
  }

  @override
  void dispose() {
    scrollController.dispose();
    searchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(notificationProvider.notifier).loadMoreNotifications();
    }
  }

  void _onSearchChanged(String value) {
    debounce.value = value;
  }

  void _onTabChanged(ImportanceLevelNotificationEnum category) {
    ref.read(notificationProvider.notifier).switchCategory(category);
  }

  void _showFilterBottomSheet() {
    final state = ref.read(notificationProvider);

    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Bộ lọc",
      height: MediaQuery.of(context).size.height * 0.7,
      child: NotificationFilterBottomSheet(
        currentFilter: state.currentFilter,
        onApplyFilter: (filter) {
          ref.read(notificationProvider.notifier).applyFilter(filter);
        },
      ),
    );
  }

  bool _hasActiveFilter() {
    final state = ref.read(notificationProvider);
    return state.currentFilter.hasFilter;
  }

  Widget _buildReadAllButton() {
    final state = ref.watch(notificationProvider);
    final hasUnreadNotifications = state.currentUnreadCount > 0;

    if (!hasUnreadNotifications) {
      return const SizedBox.shrink();
    }

    return InkWellWidget(
      onTap: () async {
        final result = await ref
            .read(notificationProvider.notifier)
            .readAllNotifications();
        if (result == true && mounted) {
          refreshData();
          AppSnackBar.showSnackBar(
            context,
            message: 'Đánh dấu tất cả là đã đọc',
            snackBarStatus: SnackBarStatus.success,
          );
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: MyAssets.icons.iconTickAll.svg(
          width: 24,
          height: 24,
          colorFilter: state.loadStatus == LoadStatus.failure
              ? ColorFilter.mode(
                  BaseColors.error,
                  BlendMode.srcIn,
                )
              : null,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutPage(
      appbar: AppBarCustom(
        title: 'Thông báo',
        actionWidget: [
          _buildReadAllButton(),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          _buildTabBar(),
          Expanded(
            child: _buildNotificationList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      color: BaseColors.backgroundWhite,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: SearchTextFieldWidget(
              controller: searchController,
              hintText: 'Tìm kiếm thông báo',
              onChanged: _onSearchChanged,
            ),
          ),
          const SizedBox(width: 16),
          InkWellWidget(
            onTap: _showFilterBottomSheet,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: BaseSpacing.spacing2,
              ),
              child: Row(
                children: [
                  _hasActiveFilter()
                      ? MyAssets.icons.iconFilterActiveS24.svg()
                      : MyAssets.icons.filter.svg(),
                  Text(
                    'Lọc',
                    style: UITextStyle.caption1Medium.copyWith(
                      color: BaseColors.textSubtitle,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    final state = ref.watch(notificationProvider);

    return Container(
      color: BaseColors.backgroundWhite,
      child: Row(
        children: [
          Expanded(
            child: _buildTab(
              category: ImportanceLevelNotificationEnum.important,
              isSelected:
                  state.selectedCategory == ImportanceLevelNotificationEnum.important,
              count: state.importantCount ?? 0,
            ),
          ),
          Expanded(
            child: _buildTab(
              category: ImportanceLevelNotificationEnum.all,
              isSelected: state.selectedCategory == ImportanceLevelNotificationEnum.all,
              count: state.totalCount ?? 0,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab({
    required ImportanceLevelNotificationEnum category,
    required bool isSelected,
    required int count,
  }) {
    final state = ref.watch(notificationProvider);
    final unreadCount = category == ImportanceLevelNotificationEnum.important
        ? state.importantUnreadCount ?? 0
        : state.allUnreadCount ?? 0;

    return InkWellWidget(
      onTap: () => _onTabChanged(category),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? BaseColors.primary : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              category.displayName,
              style: UITextStyle.body1Medium.copyWith(
                color:
                    isSelected ? BaseColors.primary : BaseColors.textSubtitle,
              ),
            ),
            if (unreadCount > 0) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0XFFFFEEEF)
                      : BaseColors.backgroundGray,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  unreadCount > 0 ? unreadCount.toString() : count.toString(),
                  style: UITextStyle.caption.copyWith(
                    color: isSelected
                        ? BaseColors.primary
                        : BaseColors.textSubtitle,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationList() {
    final state = ref.watch(notificationProvider);

    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }

    if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    }

    final notifications = state.currentNotifications ?? [];

    if (notifications.isEmpty) {
      return EmptyListWidget(
        title: state.isSearching
            ? "Không tìm thấy thông báo nào"
            : "Không có thông báo",
        onRefresh: refreshData,
      );
    }

    return Container(
      color: BaseColors.backgroundGray,
      child: RefreshIndicatorWidget(
        onRefresh: refreshData,
        child: ListView.builder(
          controller: scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.only(top: 12),
          itemCount: notifications.length + (state.isLoadingMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == notifications.length) {
              return const Padding(
                padding: EdgeInsets.all(16),
                child: Center(
                  child: LoadingIndicatorWidget(),
                ),
              );
            }

            final item = notifications[index];
            return InkWellWidget(
              onTap: () async {
                final isUnreadNotification = item.isUnread;

                if (isUnreadNotification) {
                  ref.read(notificationProvider.notifier).readNotification(
                    notificationIds: [item.id],
                  );
                }

                await context.push(
                  RouterPaths.detailNotification,
                  extra: DetailNotificationArguments(
                    notificationId: item.id,
                  ),
                );

                if (isUnreadNotification) {
                  refreshData();
                }
              },
              child: NotificationItemWidget(
                item: item,
              ),
            );
          },
        ),
      ),
    );
  }
}

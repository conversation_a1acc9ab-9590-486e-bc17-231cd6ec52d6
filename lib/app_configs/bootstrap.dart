import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:persistent_storage/persistent_storage_core.dart';
import 'package:vcc/app_configs/coordinator.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/local/secure_storage.dart';
import 'package:vcc/data/services/connectivity_service.dart';
import 'package:vcc/data/services/firebase/firebase_service.dart';
import 'package:vcc/dependencies/application_dependency.dart';
import 'package:vcc/domain/entities/notification/notification_message_entity.dart';
import 'package:vcc/domain/enums/notification_type.dart';
import 'package:vcc/domain/enums/register/category_register_enum.dart';
import 'package:vcc/firebase_options.dart';
import 'package:vcc/presentation/base/app_snack_bar.dart';
import 'package:vcc/presentation/views/features/notification/detail_notification/detail_notification_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/create_order/payment_success/payment_success_page.dart';
import 'package:vcc/utils/log_utils.dart';
import 'dart:io';

bool _isFirebaseInitialized = false;
String? _lastProcessedMessageId;
DateTime? _lastNotificationTime;

Future<void> bootstrap({required Widget app}) async {
  runZonedGuarded<Future<void>>(() async {
    WidgetsFlutterBinding.ensureInitialized();

    // set orientations
    setPreferredOrientations();

    // set style UI system
    setSystemUI();

    // Dependencies local storage
    await PersistentStorageCore.init();

    // Dependencies - GetIt
    ApplicationDependency().init();

    // init connectivity service
    await ConnectivityService().initialize();

    // init firebase
    initFirebase();
    // await NotificationService.init();

    runApp(
      ProviderScope(
        child: app,
      ),
    );
  }, (error, stackTrace) async {
    if (kDebugMode) {
      // in development, print error and stack trace
      // ignore: avoid_print
      print('$error');

      // ignore: avoid_print
      print('$stackTrace');
    } else {
      // report to a error tracking system in production
    }
  });
  // This captures errors reported by the Flutter framework.
  FlutterError.onError = (FlutterErrorDetails details) async {
    final dynamic exception = details.exception;
    final StackTrace? stackTrace = details.stack;
    if (kDebugMode) {
      // In development mode simply print to console.
      FlutterError.dumpErrorToConsole(details);
    } else {
      // In production mode report to the application zone
      if (stackTrace != null) {
        Zone.current.handleUncaughtError(exception, stackTrace);
      }
    }
  };
}

Future<void> setPreferredOrientations() {
  return SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    // DeviceOrientation.landscapeRight,
    // DeviceOrientation.landscapeLeft,
  ]);
}

void setSystemUI() {
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,

      // background status bar
      systemNavigationBarColor: Colors.white,

      // background Navigation
      statusBarIconBrightness: Brightness.dark,

      statusBarBrightness: Brightness.light,

      // set màu cho icon của status bar
      // systemNavigationBarDividerColor: Colors.orangeAccent,
    ),
  );
}

void initFirebase() async {
  if (_isFirebaseInitialized) {
    LogUtils.d('Firebase already initialized, skipping...');
    return;
  }

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Khởi tạo Firebase Services (Analytics & Crashlytics)
  await FirebaseService.instance.initialize();

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // iOS: Hiển thị system notifications khi app foreground
  // Android: Không có system notifications khi foreground (handled by app)
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,   // iOS: Hiển thị system alert khi app foreground
    badge: true,   // Update badge count
    sound: true,   // iOS: Play system sound
  );

  requestPermissions();

  setupToken();

  _isFirebaseInitialized = true;

  FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
    if (message != null) {
      if (kDebugMode) {
        print('getInitialMessage: ${message.data.toString()}');
      }
      // Delay navigation để đảm bảo app đã khởi tạo xong
      Future.delayed(const Duration(seconds: 2), () {
        _handleNotificationNavigation(message);
      });
    }
  });

  // Nhận sự kiện khi có 1 thông báo được gửi đến điện thoại
  FirebaseMessaging.onMessage.listen((RemoteMessage? message) {
    if (message == null) return;

    if (_isDuplicateNotification(message)) {
      LogUtils.d("Duplicate notification ignored: ${message.messageId}");
      return;
    }

    LogUtils.d("onMessage: ${message.data.toString()}");

    final notification = NotificationMessageEntity.fromJson(message.data);
    if (notification.getType == NotificationType.order) {
      if (notification.isSuccess) {
        if (notification.orderType == CategoryRegisterEnum.agency.keyToServer) {
          if ((GlobalData.instance.isCurrentPaymentAgencyScreen ?? false) &&
              notification.orderCode == GlobalData.instance.orderAgencyPaymentCurrent) {
            navigateToPaymentSuccess(notification);
            return; // Don't show snackbar for payment success
          }
        } else {
          navigateToPaymentSuccess(notification);
          return; // Don't show snackbar for payment success
        }
      }
    }

    // Show notification snackbar chỉ trên Android
    // iOS sẽ hiển thị system notification thay vì snackbar
    if (Platform.isAndroid) {
      _playNotificationSound();

      AppSnackBar.showNotificationFlushBar(
        AppCoordinator.context,
        title: message.notification?.title ?? '',
        content: message.notification?.body ?? '',
        onTap: (_) {
          // Navigate to notification detail when snackbar is tapped
          _handleNotificationNavigation(message);
        },
      );
    }
    // iOS: System notification sẽ tự động hiển thị và handle tap
  });

  // Nhận sự kiện click khi app đang ở chế độ background hoặc đã bị kill
  FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
    if (kDebugMode) {
      print('onMessageOpenedApp: ${message.data.toString()}');
    }
    _handleNotificationNavigation(message);
  });
}

Future<void> setupToken() async {
  // Get the token each time the application loads
  try {
    String? token = await FirebaseMessaging.instance.getToken();
    LogUtils.d("DeviceToken: $token");
    if (token != null) {
      await SecureStorage.instance.saveTokenFireBase(token);
    }
  } catch (e) {
    LogUtils.d(e);
  }
}

Future<void> requestPermissions() async {
  // Request notification permission using permission_handler
  await Permission.notification.request();

  // Request Firebase messaging permission
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );

  if (settings.authorizationStatus == AuthorizationStatus.authorized) {
    if (kDebugMode) {
      print('User granted permission');
    }
  } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
    if (kDebugMode) {
      print('User granted provisional permission');
    }
  } else {
    if (kDebugMode) {
      print('User declined or has not accepted permission');
    }
  }
}

/// Background message handler for both Android and iOS
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  if (kDebugMode) {
    print("Handling a background message: ${message.data.toString()}");
  }
}

/// Handle notification navigation for all platforms (Android & iOS)
void _handleNotificationNavigation(RemoteMessage message) {
  try {
    String? notificationId = _extractNotificationId(message);

    if (notificationId != null && notificationId.isNotEmpty) {
      AppCoordinator.context.push(
        RouterPaths.detailNotification,
        extra: DetailNotificationArguments(
          notificationId: notificationId,
        ),
      );
    } else {
      AppCoordinator.context.push(RouterPaths.notification);
    }
  } catch (e) {
    LogUtils.e('Error handling notification navigation: $e');
    AppCoordinator.context.push(RouterPaths.notification);
  }
}

/// Play notification haptic feedback (for Android snackbar)
void _playNotificationSound() {
  try {
    // Use haptic feedback for Android snackbar notifications
    HapticFeedback.lightImpact();
  } catch (e) {
    LogUtils.e('Error playing notification feedback: $e');
  }
}

/// Check if notification is duplicate (debouncing)
bool _isDuplicateNotification(RemoteMessage message) {
  final now = DateTime.now();
  final messageId = message.messageId;

  // If no messageId, can't check for duplicates
  if (messageId == null || messageId.isEmpty) {
    return false;
  }

  // Check if same messageId was processed recently (within 5 seconds)
  if (_lastProcessedMessageId == messageId &&
      _lastNotificationTime != null &&
      now.difference(_lastNotificationTime!).inSeconds < 5) {
    return true; // Duplicate
  }

  // Update tracking variables
  _lastProcessedMessageId = messageId;
  _lastNotificationTime = now;

  return false; // Not duplicate
}

/// Extract notification ID from RemoteMessage
String? _extractNotificationId(RemoteMessage message) {
  final data = message.data;

  // Check custom notificationId field
  if (data.containsKey('notificationId') && data['notificationId']?.isNotEmpty == true) {
    return data['notificationId'];
  }

  // Check id field
  // if (data.containsKey('id') && data['id']?.isNotEmpty == true) {
  //   return data['id'];
  // }

  // Check notification_id field (snake_case)
  // if (data.containsKey('notification_id') && data['notification_id']?.isNotEmpty == true) {
  //   return data['notification_id'];
  // }

  // Fallback to messageId
  // if (message.messageId?.isNotEmpty == true) {
  //   return message.messageId;
  // }

  return null;
}

void navigateToPaymentSuccess(NotificationMessageEntity notification) {
  AppCoordinator.context.push(
    RouterPaths.paymentSuccess,
    extra: PaymentSuccessArguments(
      fromAgency:
          notification.orderType == CategoryRegisterEnum.agency.keyToServer,
      orderCode: notification.orderCode,
      paymentTime: DateTime.now().toString(),
      paymentMethod: "PayCollect",
      transactionCode: notification.transactionId,
      totalPrice: int.parse(notification.amount ?? '0'),
    ),
  );
}

import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  ConnectivityResult _currentConnectivity = ConnectivityResult.none;

  // Stream controller for connectivity changes
  final StreamController<ConnectivityResult> _connectivityController =
      StreamController<ConnectivityResult>.broadcast();

  Stream<ConnectivityResult> get connectivityStream => _connectivityController.stream;
  ConnectivityResult get currentConnectivity => _currentConnectivity;
  bool get isConnected => _currentConnectivity != ConnectivityResult.none;

  Future<void> initialize() async {
    if (kDebugMode) {
      print('ConnectivityService: Initializing...');
    }
    await _initConnectivity();
    _startListening();
    if (kDebugMode) {
      print('ConnectivityService: Initialized with connectivity: $_currentConnectivity');
    }
  }

  Future<void> _initConnectivity() async {
    ConnectivityResult result = ConnectivityResult.none;
    try {
      final results = await _connectivity.checkConnectivity();
      // Take the first result or none if empty
      result = results.isNotEmpty ? results.first : ConnectivityResult.none;
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('ConnectivityService: Failed to get connectivity: $e');
      }
    }
    _currentConnectivity = result;
    _connectivityController.add(result);
  }

  void _startListening() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        // Take the first result or none if empty
        final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
        if (kDebugMode) {
          print('ConnectivityService: Connectivity changed to $result');
        }
        _currentConnectivity = result;
        _connectivityController.add(result);
      },
      onError: (error) {
        if (kDebugMode) {
          print('ConnectivityService: Error listening to connectivity: $error');
        }
      },
    );
  }

  Future<bool> checkConnectivity() async {
    await _initConnectivity();
    return isConnected;
  }

  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityController.close();
  }
}

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:vcc/domain/body/address_body.dart';
import 'package:vcc/domain/body/agency/agency_user_company_body.dart';
import 'package:vcc/domain/body/agency/approved_order_agency_body.dart';
import 'package:vcc/domain/body/agency/confirm_issue_note_body.dart';
import 'package:vcc/domain/body/agency/create_payment_body.dart';
import 'package:vcc/domain/body/agency/dealer_order_detail_body.dart';
import 'package:vcc/domain/body/agency/duplicate_order_agency_body.dart';
import 'package:vcc/domain/body/agency/history_order_agency_body.dart';
import 'package:vcc/domain/body/agency/invoice_info_body.dart';
import 'package:vcc/domain/body/agency/invoice_info_file_body.dart';
import 'package:vcc/domain/body/agency/invoice_save_info_body.dart';
import 'package:vcc/domain/body/agency/order_agency_draft_body.dart';
import 'package:vcc/domain/body/agency/order_request_cart_body.dart';
import 'package:vcc/domain/body/agency/refuse_order_agent_body.dart';
import 'package:vcc/domain/body/agency/reject_issue_note_body.dart';
import 'package:vcc/domain/body/agency/request_cancel_order_agency_body.dart';
import 'package:vcc/domain/body/agency/request_order_body.dart';
import 'package:vcc/domain/body/agency/sign_contract_invoice_body.dart';
import 'package:vcc/domain/body/agency/sync_stock_trans_body.dart';
import 'package:vcc/domain/body/agency/work_agency_body.dart';
import 'package:vcc/domain/body/answer_question_body.dart';
import 'package:vcc/domain/body/assign_association_body.dart';
import 'package:vcc/domain/body/assign_complain_body.dart';
import 'package:vcc/domain/body/assign_requirement_warranty_body.dart';
import 'package:vcc/domain/body/assign_wo_site_survey_body.dart';
import 'package:vcc/domain/body/association_customer_demand_body.dart';
import 'package:vcc/domain/body/association_customer_info_body.dart';
import 'package:vcc/domain/body/bill_stock_body.dart';
import 'package:vcc/domain/body/buy_supplies_for_customer_body.dart';
import 'package:vcc/domain/body/cancel_collection_body.dart';
import 'package:vcc/domain/body/cancel_extend_kpi_order_body.dart';
import 'package:vcc/domain/body/change_commission_body.dart';
import 'package:vcc/domain/body/change_password_body.dart';
import 'package:vcc/domain/body/check_role_user_body.dart';
import 'package:vcc/domain/body/check_serial_body.dart';
import 'package:vcc/domain/body/checklist_body.dart';
import 'package:vcc/domain/body/collection_body.dart';
import 'package:vcc/domain/body/complain_body.dart';
import 'package:vcc/domain/body/complain_extension_body.dart';
import 'package:vcc/domain/body/complain_history_mobile_call_body.dart';
import 'package:vcc/domain/body/complain_reason_extend_body.dart';
import 'package:vcc/domain/body/complain_reflect_history_body.dart';
import 'package:vcc/domain/body/contract_complain_body.dart';
import 'package:vcc/domain/body/contract_draft/approve_business_proposal_body.dart';
import 'package:vcc/domain/body/contract_draft/create_business_plan_body.dart';
import 'package:vcc/domain/body/contract_draft/create_draft_contract_body.dart';
import 'package:vcc/domain/body/contract_draft/gen_contract_code_body.dart';
import 'package:vcc/domain/body/contract_draft/gen_contract_content_body.dart';
import 'package:vcc/domain/body/contract_draft/preview_appendix_body.dart';
import 'package:vcc/domain/body/contract_draft/reject_business_proposal_body.dart';
import 'package:vcc/domain/body/contract_draft/sign_draft_contract_body.dart';
import 'package:vcc/domain/body/contract_draft/verify_draft_contract_body.dart';
import 'package:vcc/domain/body/create_complain_body.dart';
import 'package:vcc/domain/body/create_feedback_body.dart';
import 'package:vcc/domain/body/create_interact_history.dart';
import 'package:vcc/domain/body/create_requirement_warranty_body.dart';
import 'package:vcc/domain/body/customer_quote/new_customer_quote_body.dart';
import 'package:vcc/domain/body/customer_quote/quote_products_body.dart';
import 'package:vcc/domain/body/customer_quote/update_customer_quote_body.dart';
import 'package:vcc/domain/body/customer_quote/update_detail_customer_quote_body.dart';
import 'package:vcc/domain/body/customer_rating_body.dart';
import 'package:vcc/domain/body/distance_cost_body.dart';
import 'package:vcc/domain/body/explanation_body.dart';
import 'package:vcc/domain/body/explanation_detail_body.dart';
import 'package:vcc/domain/body/explanation_employee_body.dart';
import 'package:vcc/domain/body/explanation_history_body.dart';
import 'package:vcc/domain/body/explanation_penalty_body.dart';
import 'package:vcc/domain/body/explanation_reasons_body.dart';
import 'package:vcc/domain/body/explanation_total_penalty_body.dart';
import 'package:vcc/domain/body/explanation_update_body.dart';
import 'package:vcc/domain/body/export_district_product_body.dart';
import 'package:vcc/domain/body/extend_kpi_order_body.dart';
import 'package:vcc/domain/body/feedback_error_body.dart';
import 'package:vcc/domain/body/feedback_list_body.dart';
import 'package:vcc/domain/body/history_warranty_body.dart';
import 'package:vcc/domain/body/image_collection_body.dart';
import 'package:vcc/domain/body/image_order_body.dart';
import 'package:vcc/domain/body/insert_log_mobile_call_body.dart';
import 'package:vcc/domain/body/interact_history_body.dart';
import 'package:vcc/domain/body/items_amount_body.dart';
import 'package:vcc/domain/body/mobile_call_body.dart';
import 'package:vcc/domain/body/notification_body.dart';
import 'package:vcc/domain/body/notification_setting_body.dart';
import 'package:vcc/domain/body/order_request_aio_response.dart';
import 'package:vcc/domain/body/payment_body.dart';
import 'package:vcc/domain/body/reason_appointment_suggestion_body.dart';
import 'package:vcc/domain/body/reason_group_body.dart';
import 'package:vcc/domain/body/refuse_ticket_body.dart';
import 'package:vcc/domain/body/register/bank_body.dart';
import 'package:vcc/domain/body/register/contract_body.dart';
import 'package:vcc/domain/body/register/detail_collaborator_body.dart';
import 'package:vcc/domain/body/register/goods_body.dart';
import 'package:vcc/domain/body/register/management_unit_body.dart';
import 'package:vcc/domain/body/register/portrait_body.dart';
import 'package:vcc/domain/body/register/register_body.dart';
import 'package:vcc/domain/body/register/request_collaborator_body.dart';
import 'package:vcc/domain/body/report_error_body.dart';
import 'package:vcc/domain/body/request_branch_product_body.dart';
import 'package:vcc/domain/body/request_buy_product_body.dart';
import 'package:vcc/domain/body/request_buy_product_body_aio.dart';
import 'package:vcc/domain/body/request_cancel_body.dart';
import 'package:vcc/domain/body/request_district_product_body.dart';
import 'package:vcc/domain/body/request_schedule_body.dart';
import 'package:vcc/domain/body/sales_team/add_sale_member_body.dart';
import 'package:vcc/domain/body/sales_team/approve_sale_member_body.dart';
import 'package:vcc/domain/body/sales_team/confirm_join_sale_team_body.dart';
import 'package:vcc/domain/body/sales_team/update_sale_member_body.dart';
import 'package:vcc/domain/body/sales_team/validate_phone_body.dart';
import 'package:vcc/domain/body/sales_team/verify_identity_body.dart';
import 'package:vcc/domain/body/requirement_warranty/export_supplies_warranty_body.dart';
import 'package:vcc/domain/body/requirement_warranty/supplies_warranty_request_body.dart';
import 'package:vcc/domain/body/requirement_warranty/update_detail_requirement_warranty_body.dart';
import 'package:vcc/domain/body/requirement_warranty/warranty_goods_body.dart';
import 'package:vcc/domain/body/requirement_warranty/warranty_staff_body.dart';
import 'package:vcc/domain/body/save_act_product_service_body.dart';
import 'package:vcc/domain/body/save_association_body.dart';
import 'package:vcc/domain/body/save_demand_wo_body.dart';
import 'package:vcc/domain/body/save_solar_energy_body.dart';
import 'package:vcc/domain/body/save_wo_body.dart';
import 'package:vcc/domain/body/schedule_appointment_body.dart';
import 'package:vcc/domain/body/schedule_worker_body.dart';
import 'package:vcc/domain/body/search_warranty_body.dart';
import 'package:vcc/domain/body/selected_serial_complain_body.dart';
import 'package:vcc/domain/body/send_otp_body.dart';
import 'package:vcc/domain/body/send_otp_complain_body.dart';
import 'package:vcc/domain/body/sent_otp_body.dart';
import 'package:vcc/domain/body/service_order_body.dart';
import 'package:vcc/domain/body/sign_acceptance_body.dart';
import 'package:vcc/domain/body/sign_acceptance_contract_body.dart';
import 'package:vcc/domain/body/sign_complain_body.dart';
import 'package:vcc/domain/body/sign_create_warranty_body.dart';
import 'package:vcc/domain/body/sign_create_warranty_complete_body.dart';
import 'package:vcc/domain/body/sign_item_warranty_body.dart';
import 'package:vcc/domain/body/sign_work_contract_body.dart';
import 'package:vcc/domain/body/solar_energy_body.dart';
import 'package:vcc/domain/body/stock_body.dart';
import 'package:vcc/domain/body/survey_info_collection_body.dart';
import 'package:vcc/domain/body/timekeeping/approve_request_timekeeping_body.dart';
import 'package:vcc/domain/body/timekeeping/create_or_update_request_timekeeping_body.dart';
import 'package:vcc/domain/body/transfer_employee_body.dart';
import 'package:vcc/domain/body/update_commission_body.dart';
import 'package:vcc/domain/body/update_info_customer_body.dart';
import 'package:vcc/domain/body/update_installation_order_product_body.dart';
import 'package:vcc/domain/body/update_ticket_info_body.dart';
import 'package:vcc/domain/body/update_user_info_body.dart';
import 'package:vcc/domain/body/upload_report_order_body.dart';
import 'package:vcc/domain/body/user_info_body.dart';
import 'package:vcc/domain/body/voucher_body.dart';
import 'package:vcc/domain/entities/action_history_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_commission_rate_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_config_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_info_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_actual_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_invoice_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_log_inventory_response.dart';
import 'package:vcc/domain/entities/aio_contract/aio_method_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_qr_one_pay_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_collection_info_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_param_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_service_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_service_param_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_supply_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_survey_customer_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_survey_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_contract_detail_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_request_cancel_body.dart';
import 'package:vcc/domain/entities/app_param/app_param.dart';
import 'package:vcc/domain/entities/association/act_channel_source_entity.dart';
import 'package:vcc/domain/entities/association/act_customer_source_entity.dart';
import 'package:vcc/domain/entities/association/act_demand_filter_entity.dart';
import 'package:vcc/domain/entities/association/act_service_supply_entity.dart';
import 'package:vcc/domain/entities/association/act_wo_detail.dart';
import 'package:vcc/domain/entities/association/association_entity.dart';
import 'package:vcc/domain/entities/association/solar_energy_entity.dart';
import 'package:vcc/domain/entities/auth/role_entity.dart';
import 'package:vcc/domain/entities/auth/token_entity.dart';
import 'package:vcc/domain/entities/base64_entity.dart';
import 'package:vcc/domain/entities/bill_info_entity.dart';
import 'package:vcc/domain/entities/call_history_entity.dart';
import 'package:vcc/domain/entities/campaign_entity.dart';
import 'package:vcc/domain/entities/check_duplicate_order_entity.dart';
import 'package:vcc/domain/entities/check_role_user_entity.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/collection_info_entity.dart';
import 'package:vcc/domain/entities/complain/check_serial_entity.dart';
import 'package:vcc/domain/entities/complain/complain_entity.dart';
import 'package:vcc/domain/entities/complain/complain_history_mobile_call_entity.dart';
import 'package:vcc/domain/entities/complain/complain_impact_history_entity.dart';
import 'package:vcc/domain/entities/complain/complain_reason_extend_entity.dart';
import 'package:vcc/domain/entities/complain/complain_reflect_history_entity.dart';
import 'package:vcc/domain/entities/complain/complain_staff_entity.dart';
import 'package:vcc/domain/entities/complain/contract_complain_entity.dart';
import 'package:vcc/domain/entities/complain/create_contract_info_entity.dart';
import 'package:vcc/domain/entities/complain/detail_complain_entity.dart';
import 'package:vcc/domain/entities/complain/drop_down_complain_entity.dart';
import 'package:vcc/domain/entities/complain/file_sign_complain_entity.dart';
import 'package:vcc/domain/entities/complain/file_sign_interact_entity.dart';
import 'package:vcc/domain/entities/complain/mobile_call_entity.dart';
import 'package:vcc/domain/entities/complain/refuse_ticket_entity.dart';
import 'package:vcc/domain/entities/config_document_entity.dart';
import 'package:vcc/domain/entities/config_entity.dart';
import 'package:vcc/domain/entities/contract_draft/business_proposal_entity.dart';
import 'package:vcc/domain/entities/contract_draft/capacity_percent_entity.dart';
import 'package:vcc/domain/entities/contract_draft/contract_draft_code_entity.dart';
import 'package:vcc/domain/entities/contract_draft/contract_content_entity.dart';
import 'package:vcc/domain/entities/contract_draft/contract_draft_detail_entity.dart';
import 'package:vcc/domain/entities/contract_draft/contract_draft_entity.dart';
import 'package:vcc/domain/entities/contract_draft/list_approve_leader_entity.dart';
import 'package:vcc/domain/entities/contract_draft/preview_appendix_entity.dart';
import 'package:vcc/domain/entities/contract_draft/product_contract_draft_entity.dart';
import 'package:vcc/domain/entities/contract_draft/reason_type_entity.dart';
import 'package:vcc/domain/entities/contract_draft/sign_unit_info_entity.dart';
import 'package:vcc/domain/entities/count_collection_entity.dart';
import 'package:vcc/domain/entities/count_schedule_entity.dart';
import 'package:vcc/domain/entities/cus360/contract_entity.dart';
import 'package:vcc/domain/entities/cus360/csat_chart_dto.dart';
import 'package:vcc/domain/entities/cus360/feedback_dto.dart';
import 'package:vcc/domain/entities/cus360/interact_entity.dart';
import 'package:vcc/domain/entities/cus360/survey_entity.dart';
import 'package:vcc/domain/entities/cus360/ticket_entity.dart';
import 'package:vcc/domain/entities/cus360/warranty_entity.dart';
import 'package:vcc/domain/entities/customer_info_entity.dart';
import 'package:vcc/domain/entities/customer_quote/customer_quote_detail_entity.dart';
import 'package:vcc/domain/entities/customer_quote/customer_quote_entity.dart';
import 'package:vcc/domain/entities/customer_quote/quote_products_entity.dart';
import 'package:vcc/domain/entities/device_info_entity.dart';
import 'package:vcc/domain/entities/explanation/explanation_detail_entity.dart';
import 'package:vcc/domain/entities/explanation/explanation_employee_entity.dart';
import 'package:vcc/domain/entities/explanation/explanation_entity.dart';
import 'package:vcc/domain/entities/explanation/explanation_file_entity.dart';
import 'package:vcc/domain/entities/explanation/explanation_history_entity.dart';
import 'package:vcc/domain/entities/explanation/explanation_reason_entity.dart';
import 'package:vcc/domain/entities/explanation/personnel_penalty_entity.dart';
import 'package:vcc/domain/entities/feedback/feedback_entity.dart';
import 'package:vcc/domain/entities/feedback/feedback_type_entity.dart';
import 'package:vcc/domain/entities/firebase/firebase_token_model.dart';
import 'package:vcc/domain/entities/logs_entity.dart';
import 'package:vcc/domain/entities/notification/notification_category_entity.dart';
import 'package:vcc/domain/entities/notification/notification_entity.dart';
import 'package:vcc/domain/entities/notification/notification_setting_entity.dart';
import 'package:vcc/domain/entities/notification/notification_total_entity.dart';
import 'package:vcc/domain/entities/one_pay_entity.dart';
import 'package:vcc/domain/entities/order/checklist_response.dart';
import 'package:vcc/domain/entities/order/count_order_entity.dart';
import 'package:vcc/domain/entities/order/count_quote_entity.dart';
import 'package:vcc/domain/entities/order/customer_information_entity.dart';
import 'package:vcc/domain/entities/order/debt_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_item_entity.dart';
import 'package:vcc/domain/entities/order/duplicate_order_entity.dart';
import 'package:vcc/domain/entities/order/file_contract_entity.dart';
import 'package:vcc/domain/entities/order/file_entity.dart';
import 'package:vcc/domain/entities/order/file_v2_entity.dart';
import 'package:vcc/domain/entities/order/good_impact_history.dart';
import 'package:vcc/domain/entities/order/job_type_entity.dart';
import 'package:vcc/domain/entities/order/list_commission_model.dart';
import 'package:vcc/domain/entities/order/drop_down_list_entity.dart';
import 'package:vcc/domain/entities/order/list_good_entity.dart';
import 'package:vcc/domain/entities/order/order_info_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_entity.dart';
import 'package:vcc/domain/entities/order/bill_entity.dart';
import 'package:vcc/domain/entities/order/order_info_short_entity.dart';
import 'package:vcc/domain/entities/order/original_price_supply_entity.dart';
import 'package:vcc/domain/entities/order/other_service_entity.dart';
import 'package:vcc/domain/entities/order/package_entity.dart';
import 'package:vcc/domain/entities/order/reason_schedule_default_entity.dart';
import 'package:vcc/domain/entities/order/reasons_cancel_entity.dart';
import 'package:vcc/domain/entities/order/schedule_worker_entity.dart';
import 'package:vcc/domain/entities/order/serial_info_entity.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/entities/order/telesales_entity.dart';
import 'package:vcc/domain/entities/order/warning_alert_entity.dart';
import 'package:vcc/domain/entities/order_agency/agency_user_company_entity.dart';
import 'package:vcc/domain/entities/popup/popup_entity.dart';
import 'package:vcc/domain/entities/order_agency/dealer_order_detail_entity.dart';
import 'package:vcc/domain/entities/order_agency/duplicate_order_agency_entity.dart';
import 'package:vcc/domain/entities/order_agency/history_order_agency_entity.dart';
import 'package:vcc/domain/entities/order_agency/invoice_info_entity.dart';
import 'package:vcc/domain/entities/order_agency/invoice_info_file_entity.dart';
import 'package:vcc/domain/entities/order_agency/order_agency_draft_entity.dart';
import 'package:vcc/domain/entities/order_agency/order_request_cart_entity.dart';
import 'package:vcc/domain/entities/order_agency/request_order_entity.dart';
import 'package:vcc/domain/entities/order_agency/save_order_agency_entity.dart';
import 'package:vcc/domain/entities/order_agency/sign_contract_invoice_entity.dart';
import 'package:vcc/domain/entities/order_agency/trans_export_entity.dart';
import 'package:vcc/domain/entities/order_agency/work_agency_entity.dart';
import 'package:vcc/domain/entities/payment_entity.dart';
import 'package:vcc/domain/entities/price_order_entity.dart';
import 'package:vcc/domain/entities/product/category_entity.dart';
import 'package:vcc/domain/entities/product/category_package_entity.dart';
import 'package:vcc/domain/entities/product/inventory_address_info.dart';
import 'package:vcc/domain/entities/product/product_entity.dart';
import 'package:vcc/domain/entities/product/product_info_entity.dart';
import 'package:vcc/domain/entities/question_campaign_entity.dart';
import 'package:vcc/domain/entities/register/agency_entity.dart';
import 'package:vcc/domain/entities/register/bank_entity.dart';
import 'package:vcc/domain/entities/register/branch_entity.dart';
import 'package:vcc/domain/entities/register/collaborator_entity.dart';
import 'package:vcc/domain/entities/register/contract_code_entity.dart';
import 'package:vcc/domain/entities/register/detail_collaborator_entity.dart';
import 'package:vcc/domain/entities/register/file_contract_register_entity.dart';
import 'package:vcc/domain/entities/register/register_goods_entity.dart';
import 'package:vcc/domain/entities/register/otp_register_entity.dart';
import 'package:vcc/domain/entities/register/update_image_entity.dart';
import 'package:vcc/domain/entities/report_error/file_report_error.dart';
import 'package:vcc/domain/entities/report_error/group_error.dart';
import 'package:vcc/domain/entities/report_error/instruction_entity.dart';
import 'package:vcc/domain/entities/report_error/report_error_entity.dart';
import 'package:vcc/domain/entities/sales_team/revenue_sale_team_entity.dart';
import 'package:vcc/domain/entities/sales_team/sale_team_info_entity.dart';
import 'package:vcc/domain/entities/sales_team/sale_user_info_entity.dart';
import 'package:vcc/domain/entities/requirement_warranty/create_requirement_warranty_entity.dart';
import 'package:vcc/domain/entities/requirement_warranty/data_confirm_sign_entity.dart';
import 'package:vcc/domain/entities/requirement_warranty/detail_requirement_warranty_entity.dart';
import 'package:vcc/domain/entities/requirement_warranty/history_warranty_entity.dart';
import 'package:vcc/domain/entities/requirement_warranty/reason_appointment_entity.dart';
import 'package:vcc/domain/entities/requirement_warranty/list_supplies_entity.dart';
import 'package:vcc/domain/entities/requirement_warranty/order_item_warranty_entity.dart';
import 'package:vcc/domain/entities/requirement_warranty/order_warranty_entity.dart';
import 'package:vcc/domain/entities/search_store_entity.dart';
import 'package:vcc/domain/entities/send_otp_response.dart';
import 'package:vcc/domain/entities/service/service_cat_entity.dart';
import 'package:vcc/domain/entities/service/service_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/entities/stock/bill_stock_entity.dart';
import 'package:vcc/domain/entities/stock/detail_bill_stock_entity.dart';
import 'package:vcc/domain/entities/stock/inventory_entity.dart';
import 'package:vcc/domain/entities/stock/list_stock_entity.dart';
import 'package:vcc/domain/entities/stock/stock_entity.dart';
import 'package:vcc/domain/entities/stock/supply_of_bill_entity.dart';
import 'package:vcc/domain/entities/timekeeping/detail_request_timekeeping_entity.dart';
import 'package:vcc/domain/entities/timekeeping/request_timekeeping_entity.dart';
import 'package:vcc/domain/entities/timekeeping/timekeeping_schedule_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/entities/user/user_info_entity.dart';
import 'package:vcc/domain/entities/user/user_simple_entity.dart';
import 'package:vcc/domain/entities/viettel_pay_entity.dart';
import 'package:vcc/domain/entities/voucher_entity.dart';
import 'package:vcc/domain/entities/warranty/contract_warranty_entity.dart';
import 'package:vcc/domain/entities/warranty/warranty_claim_entity.dart';
import 'package:vcc/domain/entities/warranty/warranty_center_entity.dart';
import 'package:vcc/domain/entities/warranty/warranty_staff_entity.dart';
import 'package:vcc/domain/entities/work/branch_time_line_entity.dart';
import 'package:vcc/domain/entities/work/request_product_entity.dart';
import 'package:vcc/domain/entities/work/request_time_line_entity.dart';
import 'package:vcc/domain/entities/work/supply_request_product_entity.dart';
import 'package:vcc/domain/params/aio_contract/aio_check_deployment.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_actual.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_digital_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_update_param.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';
import 'package:vcc/domain/params/auth_encrypt.dart';
import 'package:vcc/domain/params/contact_info_param.dart';
import 'package:vcc/domain/params/price_order_param.dart';
import 'package:vcc/domain/params/product_order_param.dart';
import 'package:vcc/domain/responses/aio/list_data_aio_package_response.dart';
import 'package:vcc/domain/responses/data_response.dart';
import 'package:vcc/domain/responses/aio_array_response.dart';
import 'package:vcc/domain/responses/aio_data_response.dart';
import 'package:vcc/domain/responses/cim_array_response.dart';
import 'package:vcc/domain/responses/general/version_response.dart';
import 'package:vcc/domain/responses/list_data_response.dart';
import 'package:vcc/domain/responses/order/commission_confirm_response.dart';
import 'package:vcc/domain/responses/order/complete_order_response.dart';
import 'package:vcc/domain/responses/order/order_response.dart';
import 'package:vcc/domain/responses/order/warning_order_response.dart';
import 'package:vcc/domain/responses/stock/serial_response.dart';

part 'api_client.g.dart';

///API Env Develop
// const String authenticationService = 'http://10.248.243.112:8081';
// const String userProfileService = 'http://10.248.243.112:8082';
// const String orderService = 'http://10.248.243.112:8083';
// const String requirementService = 'http://10.248.243.112:8084';
// const String notificationService = 'http://10.248.243.112:8085';
// const String paymentService = 'http://10.248.243.112:8086';
// const String resourceService = 'http://10.248.243.112:8087';
// const String ticketService = 'http://10.248.243.112:8088';
// const String aioService = '$orderService/aio-service';

///API Env Staging
const String authenticationService = '/authentication-service';
const String userProfileService = '/user-profile-service';
const String resourceService = '/resource-service';
const String notificationService = '/notification-service';
const String paymentService = '/payment-service';
const String orderService = '/order-service-dangnc1';
const String requirementService = '/requirement-service';
const String aioService = '$orderService/aio-service';
const String ticketService = '/ticket-service';
const String cimService = '$ticketService/cim-service';
const String userProfileServiceAioService = '/user-profile-service/aio-service';
const String businessService = '$orderService/business-proposal';
const String contractDraftService = '$orderService/draft-contracts';
const String reasonService = '$orderService/reason-configuration';
const String timekeepingService = '$userProfileService/timekeeping';

@RestApi()
abstract class ApiClient {
  factory ApiClient(Dio dio, {String baseUrl}) = _ApiClient;

  @POST("$authenticationService/v1/login")
  Future<TokenEntity?> login({
    @Body() required AuthEncrypt authEncrypt,
  });

  @POST('$authenticationService/v1/refresh')
  Future<TokenEntity?> refreshToken({
    @Field('refreshToken') String? refreshToken,
  });

  @POST('$authenticationService/v1/logout')
  Future logout();

  @GET('$authenticationService/roles/v1')
  Future<List<RoleEntity>?> checkRoles();

  //v1 phục vụ luồng Quên mật khẩu, đăng ký tài khoản (NoAuth)
  @POST('$userProfileService/otp/v1/send')
  Future<SendOtpResponse> sendOtp({
    @Body() required SendOTPBody body,
  });

  //v2 need authentication,type = CHANGE_PASSWORD
  @POST('$userProfileService/otp/v2/send')
  Future<SendOtpResponse> sendOtpV2({
    @Body() required SendOTPBody body,
  });

  @POST('$userProfileService/otp/v1/verify')
  Future verifyOTP({
    @Body() required SendOTPBody body,
  });

  @POST('$userProfileService/users/v1/forgot-password')
  Future forgotPassword({
    @Body() required SendOTPBody body,
  });

  @GET("$userProfileService/users/v1/my-profile")
  Future<InternalStaffEntity?> getProfile();

  @GET("$userProfileService/users/v2/my-profile")
  Future<UserInfoEntity?> getProfileV2();

  @PUT("$userProfileService/users/v1/my-profile")
  Future updateUserProfile({
    @Body() required UpdateUserInfoBody body,
  });

  @GET('$userProfileService/users/v1/job-configs')
  Future<List<CodeEntity>?> getJobsSaleGroup({
    @Query("keyword") String? keyword,
    @Query("jobType") String? jobType,
  });

  @POST('$userProfileService/devices/v1/register')
  Future registerDeviceInfo(
    @Body() DeviceInfoEntity deviceInfo,
  );

  @POST('$userProfileService/users/location/v1/current')
  Future updateAddressDefault({
    @Body() required AddressBody body,
  });

  @GET("$userProfileService/users/v1/customer-360/info")
  Future<CustomerInfoEntity?> getCustomerInfo({
    @Query('customerType') required String customerType,
    @Query('phoneNumber') String? phoneNumber,
    @Query('taxCode') String? taxCode,
  });

  @GET("$userProfileService/users/v1/information")
  Future<ArrayResponse<InternalStaffEntity>?> getListStaff({
    @Query('position') int? position,
    @Query('userType') String? userType,
    @Query('phoneNumber') String? phoneNumber,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('keyword') String? keyword,
  });

  @GET("$userProfileService/users/v1/worker/branch")
  Future<ArrayResponse<InternalStaffEntity>?> getListStaffReceiving({
    @Query('provinceCode') String? provinceCode,
    @Query('userTypes') String? userTypes,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('keyword') String? keyword,
    @Query('positions') String? positions,
  });

  @GET("$userProfileService/users/v1/customer-360/info")
  Future<CustomerInfoEntity> cus360Info({
    @Query('customerType') String? customerType,
    @Query('phoneNumber') String? phoneNumber,
    @Query('taxCode') String? taxCode,
  });

  @GET("$userProfileService/users/v1/customer-360/contract")
  Future<ArrayResponse<ContractEntity>> cus360InfoContract({
    @Query('customerType') String? customerType,
    @Query('phoneNumber') String? phoneNumber,
    @Query('customerId') int? customerId,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @GET("$userProfileService/users/v1/customer-360/interaction")
  Future<InteractEntity> getInfoInteract({
    @Query('customerType') String? customerType,
    @Query('customerId') int? customerId,
  });

  @GET("$userProfileService/users/v1/customer-360/survey")
  Future<ArrayResponse<SurveyEntity>> getSurvey({
    @Query('customerType') String? customerType,
    @Query('customerId') int? customerId,
  });

  @GET("$userProfileService/users/v1/customer-360/ticket")
  Future<ArrayResponse<TicketEntity>> getTicket({
    @Query('customerType') String? customerType,
    @Query('customerId') int? customerId,
  });

  @GET("$userProfileService/users/v1/customer-360/warranty")
  Future<ArrayResponse<WarrantyEntity>> getWarranty({
    @Query('customerType') String? customerType,
    @Query('customerId') int? customerId,
  });

  @GET("$userProfileService/users/v1/information")
  Future<ArrayResponse<UserSimpleEntity>> searchUser({
    @Query('keyword') String? keyword,
    @Query('userType') String? userType,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @POST('$userProfileService/users/v1/check-profile')
  Future<CheckRoleUserEntity> checkRoleUser({
    @Body() CheckRoleUserBody? body,
  });

  @GET("$userProfileService/users/v1/tele-sale/information")
  Future<ArrayResponse<TelesalesEntity>?> getTelesales({
    @Query('keyword') String? keyword,
  });

  @POST("$userProfileService/otp/complete-order/v1/send")
  Future<SignContractEntity?> sentOtp({
    @Body() required SentOtpBody body,
  });

  @PUT("$userProfileService/users/v1/my-profile")
  Future<SignContractEntity?> updateUserInfo({
    @Body() required UserInfoBody body,
  });

  @POST("$userProfileService/users/v1/change-password")
  Future<SignContractEntity?> changePassword({
    @Body() required ChangePasswordBody body,
  });

  @GET("$resourceService/location/v1/area")
  Future<List<CodeEntity>?> getAddressInfo({
    @Query('type') required String type,
    @Query('keyword') String? keyword,
    @Query('parentCode') String? parentCode,
  });

  @POST('$resourceService/file/v1/upload')
  Future<List<String>?> uploadFile({
    @Body() required FormData body,
  });

  @POST('$resourceService/file/v1/upload-image')
  Future<List<String>?> uploadImage({
    @Body() required FormData body,
  });

  @GET('$resourceService/mobile-call-log/v1/list')
  Future<ArrayResponse<CallHistoryEntity>?> getCallHistory({
    @Query('referenceId') required String referenceId,
    @Query('functional') String? functional,
  });

  @POST('$resourceService/mobile-call-log/v1/create')
  Future createLogMobileCall({
    @Body() required MobileCallBody body,
  });

  @GET('$resourceService/affect-log/v1/lists')
  Future<ArrayResponse<ActionHistoryEntity>?> getActionHistory({
    @Query('referenceCode') String? referenceCode,
    @Query('functional') String? functional,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('sort') List<String>? sort,
  });

  @GET('$resourceService/affect-log/v1/detail/{id}')
  Future<ActionHistoryEntity?> getDetailActionHistory({
    @Path('id') String? id,
  });

  @POST('$resourceService/screen-durations/v1')
  Future saveLogs({
    @Body() required LogsEntity body,
  });

  @GET("$resourceService/file/v1/download?name=Hop-dong-giao-khoan.pdf")
  Future<FileEntity?> getOtherServicePDF();

  @GET("$resourceService/app-versions/v1")
  Future<VersionResponse?> getVersionInfo();

  @GET("$resourceService/config/v1/screen-guides")
  Future<List<ConfigDocumentEntity>?> getConfigDocument();

  @POST('$notificationService/devices/v1/token')
  Future sendFCMDeviceInfo(
    @Body() FirebaseTokenModel tokenModel,
  );

  @GET('$notificationService/popup/v1')
  Future<List<PopupEntity>?> getPopups();

  @GET("$notificationService/notification/v1/message")
  Future<ArrayResponse<NotificationEntity>?> getNotifications({
    @Query('keyword') String? keyword,
    @Query('categories') List<String>? categories,
    @Query('importanceLevel') String? importanceLevel,
    @Query('from') String? from,
    @Query('to') String? to,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @GET("$notificationService/notification/v1/total")
  Future<NotificationTotalEntity?> getNotificationTotal();

  @GET("$notificationService/notification/v1/category")
  Future<List<NotificationCategoryEntity>?> getNotificationCategories();

  @PUT("$notificationService/notification/v1/read")
  Future readNotification({
    @Body() required NotificationBody body,
  });

  @GET("$notificationService/notification/v1/message/{id}")
  Future<NotificationEntity?> getNotificationDetail({
    @Path('id') required String id,
  });

  @GET("$notificationService/notification/v1/setting")
  Future<NotificationSettingEntity?> getNotificationSettings();

  @PUT("$notificationService/notification/v1/setting")
  Future updateNotificationSetting({
    @Body() required NotificationSettingBody body,
  });

  @GET('$paymentService/payment/v1/methods')
  Future<ArrayResponse<PaymentEntity>> getPaymentMethod({
    @Query('paymentSite') String? productType,
    @Query('paymentType') String? paymentType,
  });

  @POST('$paymentService/one-pay/v1/request-collect')
  Future<OnePayEntity> createOnePayCode({
    @Body() required PaymentBody body,
  });

  @POST('$paymentService/qr/v1/request')
  Future<OnePayEntity> createPayQRCode({
    @Body() required PaymentBody body,
  });

  @POST('$paymentService/vtp/v1/request')
  Future<ViettelPayEntity> createViettelPayCode({
    @Body() required PaymentBody body,
  });

  @GET("$orderService/menu/v1/category")
  Future<ArrayResponse<ProductEntity>?> getProductByCategory(
    @Query('parentId') String parentId, {
    @Query('isAgentSelect') bool? isAgentSelect,
  });

  @GET("$orderService/store/menu/v1/category")
  Future<ArrayResponse<ProductEntity>?> getNoAuthProductByCategory({
    @Query('parentId') required String parentId,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/store/v1/search")
  Future<List<SearchStoreEntity>?> searchStore({
    @Query('keyword') String? keySearch,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('provinceCode') String? provinceCode,
    @Query('districtCode') String? districtCode,
  });

  @GET("$orderService/suggest/v1")
  Future<ArrayResponse<ProductInfoEntity>?> getSuggestProductStore({
    @Query('keyword') String? keySearch,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('isAgentSelect') bool? isAgentSelect,
  });

  @GET("$orderService/store/suggest/v1")
  Future<ArrayResponse<ProductInfoEntity>?> getNoAuthSuggestProductStore({
    @Query('keyword') String? keySearch,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/products/v1/menu")
  Future<ArrayResponse<ProductInfoEntity>?> getProductByMenu({
    @Query('menuId') String? menuId,
    @Query('provinceCode') String? provinceCode,
    @Query('districtCode') String? districtCode,
    @Query('wardCode') String? wardCode,
    @Query('startTime') String? startTime,
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @GET("$orderService/products/v1/category")
  Future<ProductEntity?> searchProductByCategory({
    @Query('categoryId') required String categoryId,
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('minPrice') int? minPrice,
    @Query('maxPrice') int? maxPrice,
    @Query('provinceCode') int? provinceCode,
    @Query('isAgentSelect') bool? isAgentSelect,
  });

  @GET("$orderService/store/products/v1/category")
  Future<ProductEntity?> searchNoAuthProductByCategory({
    @Query('categoryId') required String categoryId,
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('minPrice') int? minPrice,
    @Query('maxPrice') int? maxPrice,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/products/v1/all")
  Future<ProductEntity?> getAllProduct({
    @Query('keyword') String? keyword,
    @Query('isAgentSelect') bool? isAgentSelect,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @GET("$orderService/store/products/v1/all")
  Future<ProductEntity?> getNoAuthAllProduct({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/products/v1/product-detail/{code}")
  Future<ProductInfoEntity?> getProductDetail(
    @Path('code') String code, {
    @Query('isAgentSelect') bool? isAgentSelect,
  });

  @GET("$orderService/store/products/v1/product-detail/{code}")
  Future<ProductInfoEntity?> getNoAuthProductDetail({
    @Path('code') required String code,
    @Query('provinceCode') String? provinceCode,
  });

  @POST('$orderService/contacts/v1/create')
  Future saveContactInfo(
    @Body() ContactInfoParam param,
  );

  @POST("$orderService/schedule/v1/workers/oft3gk")
  Future<ArrayResponse<InternalStaffEntity>?> getListOFT3({
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('keyword') String? keyword,
    @Query('startTime') String? startTime,
  });

  @GET("$orderService/schedule/v1/workers")
  Future<ArrayResponse<InternalStaffEntity>?> getListWorker({
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('keyword') String? keyword,
    @Query('provinceCode') String? provinceCode,
    @Query('districtCode') String? districtCode,
    @Query('startTime') String? startTime,
    @Query('orderType') String? orderType,
    @Query('searchWorkerType') String? workerType,
  });

  @GET('$orderService/orders/v1/check-duplicates')
  Future<CheckDuplicateOrderEntity?> checkExistOrder({
    @Query('customerId') String? customerId,
  });

  @GET('$orderService/inventory/v1/remain')
  Future<ArrayResponse<InventoryAddressInfo>> getInventoryAddress({
    @Query('provinceCode') required String provinceCode,
    @Query('code') required String productCode,
    @Query('keyword') String? keySearch,
  });

  @POST('$orderService/orders/v1')
  Future<OrderResponse?> createOrder({
    @Body() required OrderParam param,
  });

  @PUT('$orderService/orders/v1')
  Future editServiceOrder({
    @Body() required ServiceOrderBody body,
  });

  @POST('$orderService/orders/product/v1')
  Future<OrderResponse?> createOrderProduct({
    @Body() required OrderParam param,
  });

  @POST('$orderService/orders/v1/total-amount')
  Future<PriceOrderEntity?> getPriceOrder({
    @Body() required PriceOrderParam body,
  });

  @POST('$orderService/orders/v1/items-amount')
  Future<OrderInfoEntity?> getItemUpdated({
    @Body() required ItemsAmountBody body,
  });

  @GET("$orderService/menu/v1/all")
  Future<ArrayResponse<CategoryEntity>?> getProductCategories({
    @Query('type') required String type,
    @Query('isAgentSelect') bool? isAgentSelect,
  });

  @GET("$orderService/store/menu/v1")
  Future<ArrayResponse<CategoryEntity>?> getNoAuthProductCategories({
    @Query('type') required String type,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/menu/v1/all")
  Future<ArrayResponse<CategoryPackageEntity>?> getServiceCategories({
    @Query('type') required String type,
    @Query('tenantId ') String? tenantId,
  });

  @GET("$orderService/store/menu/v1")
  Future<ArrayResponse<CategoryPackageEntity>?> getNoAuthServiceCategories({
    @Query('type') required String type,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/menu/v1/category")
  Future<List<ServiceEntity>?> getServiceByCategory({
    @Query('parentId') required String parentId,
  });

  @GET("$orderService/store/menu/v1/category")
  Future<List<ServiceEntity>?> getNoAuthServiceByCategory({
    @Query('parentId') required String parentId,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/menu/v1/all")
  Future<ServiceEntity?> getAllService({
    @Query('categoryId') String? categoryId,
    @Query('type') String? type,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('keyword') String? keyword,
  });

  @GET("$orderService/store/menu/v1")
  Future<ServiceEntity?> getNoAuthAllService({
    @Query('categoryId') String? categoryId,
    @Query('type') String? type,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('keyword') String? keyword,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/menu/service/v1/category")
  Future<ServiceCatEntity?> getChildService(
    @Query('parentId') String parentId,
  );

  @GET("$orderService/store/menu/service/v1/category")
  Future<ServiceCatEntity?> getNoAuthChildService({
    @Query('parentId') required String parentId,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/services/v1/search")
  Future<ArrayResponse<ServiceInfoEntity>?> searchService({
    @Query('keyword') String? keyword,
    @Query('categoryId') String? categoryId,
    @Query('menuId') String? menuId,
    @Query('serviceType') String? serviceType,
    @Query('startTime') String? startTime,
    @Query('provinceCode') String? provinceCode,
    @Query('districtCode') String? districtCode,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @GET("$orderService/store/services/v1/search")
  Future<ArrayResponse<ServiceInfoEntity>?> searchNoAuthService({
    @Query('keyword') String? keyword,
    @Query('categoryId') String? categoryId,
    @Query('menuId') String? menuId,
    @Query('serviceType') String? serviceType,
    @Query('startTime') String? startTime,
    @Query('provinceCode') String? provinceCode,
    @Query('districtCode') String? districtCode,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @GET("$orderService/packages/v1/{code}")
  Future<ServiceInfoEntity?> getServicePackageInfo({
    @Path('code') required String code,
  });

  @GET("$orderService/products/v1/product-detail/{code}")
  Future<ServiceInfoEntity?> getServiceDetail({
    @Path('code') required String code,
  });

  @POST("$orderService/coupons/v1/my-coupon")
  Future<ArrayResponse<VoucherEntity>?> getMyCoupon({
    @Body() required VoucherBody body,
  });

  @GET("$orderService/menu/service/v1/package")
  Future<ArrayResponse<ServiceInfoEntity>> searchServicePackage({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/store/menu/service/v1/package")
  Future<ArrayResponse<ServiceInfoEntity>> searchNoAuthServicePackage({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/orders/v1/pages")
  Future<ArrayResponse<OrderInfoShortEntity>?> getShortOrders({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('orderStatus') String? status,
    @Query('startTime') String? startTime,
    @Query('endTime') String? endTime,
    @Query('participantTypes') List<String>? participantTypes,
    @Query('orderTypes') List<String>? orderTypes,
    @Query('provinceCode') String? provinceCode,
    @Query('districtCode') List<String>? districtCode,
    @Query('specialQueryType') String? specialQueryType,
  });

  @GET('$orderService/association-demand-config/v1/service')
  Future<ArrayResponse<ActServiceSupplyEntity>?> getAssociationService({
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('sort') List<String>? sort,
    @Query('keyword') String? keyword,
  });

  @GET('$orderService/association-demand-config/v1/product')
  Future<ArrayResponse<ActServiceSupplyEntity>?> getAssociationProduct({
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('sort') List<String>? sort,
    @Query('keyword') String? keyword,
  });

  @GET("$orderService/supplies/v1/search")
  Future<ArrayResponse<SupplyEntity>?> searchSupply({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('supplyTypes') String? supplyTypes,
    @Query('wmsTypes') String? wmsTypes,
    @Query('inventoryTypes') String? inventoryTypes,
  });

  @GET("$orderService/store/supplies/v1/search")
  Future<ArrayResponse<SupplyEntity>?> searchNoAuthSupply({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('supplyTypes') String? supplyTypes,
    @Query('wmsTypes') String? wmsTypes,
    @Query('inventoryTypes') String? inventoryTypes,
    @Query('provinceCode') String? provinceCode,
  });

  @GET("$orderService/orders/v1/order/search")
  Future<ArrayResponse<OriginalPriceSupplyEntity>?> searchSupplyOrder({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('listStockType') String? listStockType,
    @Query('goodsCode') String? goodsCode,
    @Query('orderCode') String? orderCode,
  });

  @GET("$orderService/packages/detail/v1")
  Future<PackageEntity> getPackageDetail({
    @Query('code') required String code,
    @Query('startTime') required String startTime,
    @Query('provinceCode') required String provinceCode,
    @Query('districtCode') String? districtCode,
  });

  @GET("$orderService/services/v1/detail")
  Future<ServiceInfoEntity> getServiceInfo({
    @Query('code') required String code,
  });

  @GET("$orderService/services/v1/other")
  Future<ArrayResponse<OtherServiceEntity>> getOtherService({
    @Query('menuCode') required String menuCode,
    @Query('serviceCode') required String serviceCode,
  });

  @GET("$orderService/services/v1/package-addition")
  Future<ArrayResponse<ServiceInfoEntity>> getServicePackageAddition({
    @Query('keyword') String? keyword,
    @Query('parentCode') required String parentCode,
    @Query('serviceType') String? serviceType,
    @Query('startTime') String? startTime,
    @Query('provinceCode') String? provinceCode,
    @Query('districtCode') String? districtCode,
    @Query('wardCode') String? wardCode,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('sort') List<String>? sort,
  });

  @GET("$orderService/bills/v1/pages")
  Future<ArrayResponse<BillEntity>> billSearch({
    @Query('keyword') String? keyword,
    @Query('status') String? status,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @GET("$orderService/bills/v1/report")
  Future<BillEntity> billReport({
    @Query('orderCode') String? orderCode,
  });

  @GET("$orderService/bills/v1/detail")
  Future<BillDetailEntity> billDetail({
    @Query('orderCode') String? orderCode,
  });

  @POST("$orderService/bills/v1/preview")
  Future<BillDetailEntity> billPreview({
    @Body() BillDetailEntity? data,
  });

  @GET("$orderService/debt/v1")
  Future<DebtEntity> getDebts({
    @Query('queryType') String? queryType,
  });

  @PUT("$orderService/bills/v1")
  Future updateBill({
    @Body() BillDetailEntity? data,
  });

  @POST("$orderService/stock-trans/v1/stock")
  Future<List<StockEntity>?> getListStock({
    @Body() required StockBody body,
  });

  @GET("$orderService/stock-trans/v1/stock")
  Future<List<StockEntity>?> getListStockRequestOrder({
    @Query('listType') List<String>? listType,
    @Query('orderCode') String? orderCode,
    @Query('levelStock') List<String>? levelStock,
  });

  @GET("$orderService/stock-trans/v1/supply")
  Future<ArrayResponse<SupplyEntity>?> getListSupply({
    @Query('keyword') String? keyword,
    @Query('status') String? status,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('stockType') String? stockType,
  });

  @GET("$orderService/stock-trans/v1/goods-cam-and-wifi")
  Future<ArrayResponse<SupplyEntity>?> getListSupplyDistrict({
    @Query('keyword') String? keyword,
    @Query('status') String? status,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('stockType') String? stockType,
  });

  @GET("$orderService/stock-trans/v1/serial-user-stock")
  Future<List<SerialInfoEntity>?> getListSerial({
    @Query('goodsCode') String? goodsCode,
    @Query('listStockType') String? listStockType,
    @Query('keyword') String? keyword,
  });

  @GET("$orderService/stock-trans/v1/serial-stock-trans")
  Future<SerialResponse?> getSerialsOfOrder({
    @Query('stockTransDetailId') int? stockTransDetailId,
  });

  @GET("$orderService/stock-trans/v1/to-confirm")
  Future<ArrayResponse<BillStockEntity>?> getListConfirmationBill({
    @Query('keyWord') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('businessType') String? businessType,
    @Query('status') String? status,
    @Query('listTypeStock') String? listTypeStock,
  });

  @GET("$orderService/stock-trans/v1/detail/{id}")
  Future<List<SupplyOfBillEntity>?> getSupplyOfStockBill({
    @Path('id') int? id,
  });

  @GET("$orderService/stock-trans/v1/by-created")
  Future<ArrayResponse<BillStockEntity>?> getListMyCreatedBill({
    @Query('keyWord') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('businessType') String? businessType,
    @Query('status') String? status,
    @Query('listTypeStock') String? listTypeStock,
  });

  @GET("$orderService/bills/v1/customer")
  Future<BillInfoEntity?> getBillCustomerInfo({
    @Query('orderCode') required String orderCode,
  });

  @PUT("$orderService/bills/v1/customer")
  Future updateBillCustomerInfo({
    @Body() BillInfoParam? body,
  });

  @PUT("$orderService/bills/v1/unsent")
  Future cancelBillOrder({
    @Query('orderCode') String? orderCode,
  });

  @GET("$orderService/stock-trans/v1/personal-inventory")
  Future<ArrayResponse<InventoryEntity>?> getPersonalInventory({
    @Query('keyword') String? keyword,
    @Query('goodsCode') String? goodsCode,
    @Query('type') String? type,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @PUT("$orderService/stock-trans/v1/update-confirmation")
  Future updateBillStatus({
    @Body() required BillStockBody body,
  });

  @GET("$orderService/purchase/v1")
  Future<ArrayResponse<RequestProductEntity>?> getListRequestProduct({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('type') String? type,
    @Query('status') String? status,
  });

  @GET("$orderService/purchase/v1/{id}")
  Future<ArrayResponse<SupplyRequestProductEntity>?>
      getListSupplyRequestProduct({
    @Path('id') int? id,
    @Query('type') String? type,
  });

  @GET("$orderService/purchase/v1/branch/time-line/{id}")
  Future<ArrayResponse<BranchTimeLineEntity>?> getTimeLineBranchRequest({
    @Path('id') int? id,
  });

  @GET("$orderService/purchase/v1/corporation/time-line/{id}")
  Future<RequestTimeLineEntity?> getTimeLineCompanyRequest({
    @Path('id') int? id,
  });

  @POST("$orderService/purchase/v1")
  Future createRequestBuyProduct({
    @Body() required RequestBuyProductBody body,
  });

  @POST("$aioService/service/orderRequest/b2c/saveOrderRequest")
  Future<OrderRequestAioResponse> saveOrderRequestAio({
    @Body() required RequestBuyProductBodyAio body,
  });

  @GET("$orderService/purchase/v1/goods")
  Future<ArrayResponse<SupplyRequestProductEntity>?> getListSupplyOrderRequest({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('type') String? type,
    @Query('modemIds') List<int>? modemIds,
    @Query('manufacturerIds') List<int>? manufacturerIds,
    @Query('goodsGroupIds') List<int>? goodsGroupIds,
  });

  @GET("$orderService/purchase/v1/manufacturer")
  Future<ArrayResponse<CodeEntity>?> getProductsManufacturers({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @GET("$orderService/purchase/v1/modem")
  Future<ArrayResponse<CodeEntity>?> getProductModerns({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @GET("$orderService/purchase/v1/goods-group")
  Future<ArrayResponse<CodeEntity>?> getProductGroup({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @POST("$orderService/stock-trans/v1/district-export-for")
  Future createRequestDistrictProduct({
    @Body() required RequestDistrictProductBody body,
  });

  @POST("$orderService/stock-trans/v1/district-export-return")
  Future exportDistrictProduct({
    @Body() required ExportDistrictProductBody body,
  });

  @POST("$orderService/stock-trans/v1/branch-export-for")
  Future createRequestBranchProduct({
    @Body() required RequestBranchProductBody body,
  });

  @POST("$orderService/stock-trans/v1/branch-export-return")
  Future exportBranchProduct({
    @Body() required RequestBranchProductBody body,
  });

  @POST("$orderService/stock-trans/v1/employee_transfer")
  Future transferEmployee({
    @Body() required TransferEmployeeBody body,
  });

  @POST("$orderService/stock-trans/v1/transfer-deployment-employee")
  Future transferDeploymentEmployee({
    @Body() required TransferEmployeeBody body,
  });

  @PUT("$orderService/stock-trans/v1/transfer-deployment-employee/{id}")
  Future confirmTransferDeploymentEmployee({
    @Path("id") int? stockTransId,
  });

  @GET("$orderService/orders/v1")
  Future<DetailOrderEntity?> getOrderDetail({
    @Query('orderCode') String? orderCode,
    @Query('isGetOldOrder') bool? isGetOldOrder,
  });

  @PUT("$orderService/orders/v1/worker-assign")
  Future assignWorker({
    @Query('orderCode') required String orderCode,
    @Query('userCode') required String userCode,
    @Query('type') required String type,
  });

  @GET("$orderService/commission/v1")
  Future<ListCommissionModel> getCommissionList({
    @Query('status') String? status,
    @Query('from') String? from,
    @Query('to') String? to,
    @Query('orderType') List<String>? orderType,
    @Query('keyword') String? keyword,
  });

  @POST("$aioService/service/payment/getQrOnePay")
  Future<AioQrOnePayEntity> getQrOnePay({
    @Body() required AioContractParam body,
  });

  @PUT("$orderService/commission/v1/approval")
  Future changeConfirmCommission({
    @Body() required ChangeCommissionBody body,
  });

  @PUT("$orderService/commission/v1")
  Future updateCommission({
    @Body() required UpdateCommissionBody body,
  });

  @PUT("$orderService/orders/v1/customer")
  Future updateInfoCustomer({
    @Body() required UpdateInfoCustomerBody body,
  });

  @PUT("$orderService/orders/v1/next-steps")
  Future updateOrder({
    @Query('orderCode') String? orderCode,
  });

  @GET("$orderService/orders/v1/count")
  Future<CountOrderEntity?> getCountOrders({
    @Query('orderTypes') List<String>? orderTypes,
    @Query('participantTypes') List<String>? participantTypes,
    @Query('startTime') String? startTime,
    @Query('endTime') String? endTime,
    @Query('keyword') String? keyword,
    @Query('provinceCode') String? provinceCode,
    @Query('districtCode') List<String>? districtCode,
  });

  @GET("$orderService/quotes/v1/count")
  Future<CountQuoteEntity?> getCountQuotes();

  @GET("$orderService/orders/v1/count-amount")
  Future<CompleteOrderResponse?> getTotalCompleteOrders({
    @Query('orderTypes') List<String>? orderTypes,
    @Query('startTime') String? startTime,
  });

  @GET("$orderService/orders/v1/customer")
  Future<CustomerInformationEntity?> getDetailInfoCustomer({
    @Query('orderCode') String? orderCode,
  });

  @GET("$orderService/orders/aio/v1/job-types")
  Future<ArrayResponse<JobTypeEntity>?> getJobType({
    @Query('keyword') String? keyword,
  });

  @PUT("$orderService/orders/v1/tele-assign")
  Future<ArrayResponse<TelesalesEntity>?> assignTelesales({
    @Query('orderCode') String? orderCode,
    @Query('userCode') String? userCode,
  });

  @GET("$orderService/orders/aio/v1/reasons-cancel")
  Future<ArrayResponse<ReasonCancelEntity>?> getReasonsCancel({
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('parentId') String? parentId,
    @Query('queryType') String? queryType,
  });

  @PUT("$orderService/orders/v1/confirm-call")
  Future updateCall({
    @Query('orderCode') required String orderCode,
  });

  @GET("$orderService/orders/v1/check-duplicates")
  Future<DuplicateOrderEntity> getOrderDuplicates({
    @Query('customerId') required String customerId,
  });

  @GET("$orderService/orders/v1/goods-impact-history")
  Future<ArrayResponse<GoodImpactHistory>> getGoodImpactHistory({
    @Query('orderCode') required String orderCode,
  });

  @PUT("$orderService/orders/v1/request-cancel")
  Future requestCancel({
    @Body() required RequestCancelBody body,
  });

  @PUT("$orderService/orders/v1/request-cancel/ignore")
  Future ignoreRequestCancel({
    @Query('requestCancelOrderId') required String requestCancelOrderId,
  });

  @PUT("$orderService/orders/v1/schedule")
  Future scheduleCustomer({
    @Body() required RequestScheduleBody body,
  });

  @GET("$orderService/orders/v1/schedule")
  Future<CountEntity?> countSchedule({
    @Query('orderCode') required String orderCode,
  });

  @GET("$orderService/orders/v1/request-cancel")
  Future<CountEntity?> countRequestCancelOrder({
    @Query('orderCode') required String orderCode,
  });

  @GET("$orderService/orders/v1/request-extend-kpi")
  Future<CountEntity?> countRequestExtendKpiOrder({
    @Query('orderCode') required String orderCode,
  });

  @GET("$orderService/orders/v1/worker-assign")
  Future<CountEntity?> countWorkerAssignOrder({
    @Query('orderCode') required String orderCode,
  });

  @PUT("$orderService/orders/v1/cancel-receive")
  Future cancelReceive({
    @Body() required RequestCancelBody body,
  });

  @PUT("$orderService/orders/v1/receive")
  Future receiveOrder({
    @Query('orderCode') required String orderCode,
  });

  @PUT("$orderService/orders/v1/ignore")
  Future ignoreOrder({
    @Query('orderCode') required String orderCode,
  });

  @PUT("$orderService/orders/v1/request-extend-kpi")
  Future extendKpiOrder({
    @Body() required ExtendKpiOrderBody body,
  });

  @PUT("$orderService/orders/v1/cancel-extend-kpi")
  Future cancelExtendKpiOrder({
    @Body() required CancelExtendKpiOrderBody body,
  });

  @PUT("$orderService/orders/v1/partner-order")
  Future calculateShippingCost({
    @Body() required DistanceCostBody body,
  });

  @GET("$orderService/orders/v1/get-file")
  Future<SignContractEntity?> getFileContract({
    @Query('orderCode') required String orderCode,
  });

  @POST("$orderService/orders/v1/confirm-acceptance")
  Future signAcceptance({
    @Body() required SignAcceptanceBody body,
  });

  @POST("$orderService/orders/v2/confirm-acceptance")
  Future uploadReportOrder({
    @Body() required UploadReportOrderBody body,
  });

  @POST('$orderService/orders/v1/upload-image')
  Future<List<String>?> uploadOrderImage({
    @Body() required FormData body,
  });

  @POST('$orderService/orders/v1/delete-image')
  Future<List<String>?> deleteOrderImage({
    @Body() required ImageOrderBody body,
  });

  @GET("$orderService/orders/v1/reasons-schedule")
  Future<ReasonScheduleDefaultEntity?> getReasonsSchedule({
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('orderCode') String? orderCode,
    @Query('scheduleTime') String? scheduleTime,
    @Query('parentId') String? parentId,
  });

  @GET("$orderService/orders/v1/cancel-reasons")
  Future<ArrayResponse<ReasonCancelEntity>?> getCancelReasonsSchedule({
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('parentId') String? parentId,
  });

  @PUT("$orderService/orders/v1/cancel-schedule")
  Future cancelSchedule({
    @Body() required RequestScheduleBody body,
  });

  @GET("$orderService/stock-trans/v1/{stockTransId}")
  Future<DetailBillStockEntity> getDetailStockBill({
    @Path('stockTransId') int? stockTransId,
  });

  @GET("$orderService/orders/v1/validate-cancel-receive")
  Future<DetailBillStockEntity> validateCancelReceive({
    @Query('orderCode') String? orderCode,
  });

  @GET("$orderService/orders/v1/check-list")
  Future<List<ChecklistResponse>?> getCheckList({
    @Query('orderCode') String? orderCode,
    @Query('serviceCode') String? serviceCode,
  });

  @PUT("$orderService/orders/v1/check-list")
  Future updateCheckList({
    @Body() required ChecklistBody body,
  });

  @PUT("$orderService/orders/product/v1")
  Future updateInstallationOrderProduct({
    @Body() required UpdateInstallationOrderProductBody body,
  });

  @POST("$orderService/orders/v1/aio/send-update-order-otp")
  Future<SignContractEntity?> sentOtpUpdateOrder({
    @Body() required SendOTPBody body,
  });

  @POST('$orderService/orders/v1/aio/verify-update-order-otp')
  Future verifyOtpUpdateOrder({
    @Body() required SendOTPBody body,
  });

  @PUT('$orderService/orders/v1/complete')
  Future completeOrder({
    @Query('orderCode') String? orderCode,
  });

  @GET('$orderService/alerts/v1/expired/pages')
  Future<WarningOrderResponse?> getWarningOrder({
    @Query('expiredAlertTypes') String? expiredAlertTypes,
  });

  @GET('$orderService/alerts/v1/expired')
  Future<WarningAlertEntity?> checkShowWarningOrder();

  @GET('$orderService/products/v1/product-service')
  Future<ArrayResponse<DetailOrderItemEntity>?> getServiceByproduct({
    @Query('productCode') String? productCode,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @POST("$orderService/orders/v1/multi-payment")
  Future validateMultiPayment({
    @Body() required OrderParam body,
  });

  @POST('$orderService/orders/v1/validate')
  Future validateOrder({
    @Query('orderCode') required String orderCode,
  });

  @POST("$orderService/orders/v2/confirm-acceptance")
  Future signAcceptanceV2({
    @Body() required SignAcceptanceBody body,
  });

  @PUT("$orderService/orders/v1/partner-order/material-request")
  Future materialRequest({
    @Body() required SignAcceptanceBody body,
  });

  @GET('$orderService/order-types/v1')
  Future<ArrayResponse<String>?> getOrderType({
    @Query('orderTypes') String? orderTypes,
    @Query('customerTypes') String? customerTypes,
  });

  @POST('$orderService/schedule/v1')
  Future<List<ScheduleWorkerEntity>> checkScheduleWorker({
    @Body() required ScheduleWorkerBody body,
  });

  @GET("$orderService/supplies/combo/v1/search")
  Future<ArrayResponse<ServiceInfoEntity>> getSuppliesCombo({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @GET("$orderService/supplies/combo/v1/detail")
  Future<PackageEntity?> getDetailSuppliesComboInfo({
    @Query('comboCode') required String comboCode,
  });

  @GET("$orderService/schedule/v1/check-hot-order")
  Future<ScheduleWorkerEntity?> checkHotOrder({
    @Query('orderType') required String orderType,
    @Query('districtCode') required String districtCode,
    @Query('startTime') required String startTime,
    @Query('packageCode') String? packageCode,
  });

  @GET("$orderService/alerts/v1/mobile-call")
  Future<WarningAlertEntity?> showWarningMobileCall({
    @Query("orderCode") required String orderCode,
  });

  @GET("$orderService/alerts/v1/commissions")
  Future<CommissionConfirmResponse?> checkCommissionConfirm({
    @Query("customerPhone") required String customerPhone,
  });

  @GET("$notificationService/bottom-sheet/v1")
  Future<dynamic> getBottomSheetAlerts();

  @GET("$orderService/alerts/v1/orders")
  Future<dynamic> getAlertOrders({
    @Query("alertBottomSheetType") required String alertBottomSheetType,
    @Query("alertDeadlineType") String? alertDeadlineType,
  });

  @GET("$requirementService/campaign/v1")
  Future<ArrayResponse<CampaignEntity>?> getListCampaign({
    @Query('keyword') String? keyword,
    @Query('targetCustomerType') String? targetCustomerType,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @GET("$requirementService/campaign/v1/question")
  Future<ArrayResponse<QuestionCampaignEntity>?> getListQuestionOfCampaign({
    @Query('campaignId') String? campaignId,
  });

  @PUT("$requirementService/survey/v1/update-survey-info")
  Future updateCollectionInfo({
    @Body() required SurveyCollectionBody body,
  });

  @PUT("$requirementService/survey/v1/receive")
  Future receiveCollection({
    @Body() required List<String> body,
  });

  @PUT("$requirementService/survey/v1/complete")
  Future completeCollection({
    @Body() required AnswerQuestionBody body,
  });

  @PUT("$requirementService/survey/v1/cancel")
  Future cancelCollection({
    @Body() required CancelCollectionBody body,
  });

  @POST('$requirementService/survey/v1/delete-images')
  Future deleteImageCollection(
    @Body() ImageCollectionBody body,
  );

  @POST('$requirementService/survey/v1/create')
  Future<CollectionInfoEntity?> createCollection(
    @Body() CollectionBody body,
  );

  @GET('$requirementService/survey/v1/search')
  Future<ArrayResponse<CollectionInfoEntity>?> getListCollection({
    @Query('keySearch') String? keySearch,
    @Query('campaignId') String? campaignId,
    @Query('status') List<String>? status,
    @Query('customerType') String? customerType,
    @Query('districtCodes') List<String>? districtCodes,
    @Query('isGetAll') bool? isGetAll,
    @Query('assignment') String? assignment,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @GET('$requirementService/association/v1/search')
  Future<ArrayResponse<AssociationEntity>?> searchAssociation({
    @Query('from') String? from,
    @Query('to') String? to,
    @Query('demandCode') List<String>? demandCode,
    @Query('status') List<String>? listStatus,
    @Query('types') List<String>? types,
    @Query('processStatus') List<String>? processStatus,
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('sort') List<String>? sort,
  });

  @GET('$requirementService/survey/v1/detail/{code}')
  Future<CollectionInfoEntity?> getDetailCollection(
    @Path() String code,
  );

  @GET('$requirementService/survey/v1/aggregate-status')
  Future<CountCollectionEntity?> countCollectionStatus({
    @Query('keySearch') String? keySearch,
    @Query('campaignId') String? campaignId,
    @Query('status') List<String>? status,
    @Query('customerType') String? customerType,
    @Query('districtCodes') List<String>? districtCodes,
    @Query('isGetAll') bool? isGetAll,
  });

  @GET('$requirementService/association/v1/detail/{code}')
  Future<AssociationEntity?> getDetailAssociation(
    @Path() String code,
  );

  @GET('$requirementService/config/v1/customer-source')
  Future<ArrayResponse<ActCustomerSourceEntity>?> getAssociationCustomerSource({
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('sort') List<String>? sort,
  });

  @GET('$requirementService/config/v1/channel')
  Future<ArrayResponse<ActChannelSourceEntity>?> getAssociationChannel({
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('sort') List<String>? sort,
  });

  @PUT('$requirementService/association/v1/receive/{code}')
  Future receiveAssociation({
    @Path() required String code,
  });

  @PUT('$requirementService/association/v1/assign')
  Future assignAssociation({
    @Body() required AssignAssociationBody body,
  });

  @PUT('$requirementService/association/v1/customer-info')
  Future updateCustomerInfo({
    @Body() required AssociationCustomerInfoBody body,
  });

  @PUT('$requirementService/association/v1/customer-demand')
  Future<AssociationEntity> updateCustomerDemand({
    @Body() required AssociationCustomerDemandBody body,
  });

  @PUT('$requirementService/association/v1/complete/{code}')
  Future completeAssociation({
    @Path() required String code,
  });

  @POST('$requirementService/association/v1/create')
  Future<AssociationEntity> createAssociation({
    @Body() required SaveAssociationBody body,
  });

  @GET('$requirementService/association/v1/demand-filter')
  Future<ArrayResponse<ActDemandFilterEntity>?> getDemandFilter();

  @GET('$requirementService/config/v1')
  Future<ArrayResponse<ConfigEntity>?> getConfigEntity({
    @Query('type') String? type,
    @Query('page') int? page,
    @Query('size') int? size,
  });

  @PUT('$requirementService/wo/site-survey/v1/update-demand')
  Future updateWoSiteSurvey({
    @Body() required SaveDemandWoBody body,
  });

  @GET('$requirementService/wo/site-survey/v1/survey-demand/{code}')
  Future<ArrayResponse<ActDemandFilterEntity>?> getDemandWoSurvey({
    @Path() required String code,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('keyword') String? keyword,
  });

  @PUT('$requirementService/wo/site-survey/v1/receive/{code}')
  Future receiveWoSurvey({
    @Path() required String code,
  });

  @PUT('$requirementService/wo/site-survey/v1/assign')
  Future assignWOSurvey({
    @Body() required AssignAssociationBody body,
  });

  @PUT('$requirementService/wo/site-survey/v1/assign')
  Future assignWoSiteSurvey({
    @Body() required AssignWoSiteSurveyBody body,
  });

  @GET('$requirementService/wo/site-survey/v1/aggregate-status')
  Future aggregateStatusWoSurvey({
    @Query('keyword') String? keyword,
    @Query('from') String? from,
    @Query('to') String? to,
    @Query('processStatus') String? processStatus,
    @Query('status') String? status,
  });

  @POST('$requirementService/wo/site-survey/v1/create')
  Future<AssociationEntity> createWoSurvey({
    @Body() required SaveWoBody body,
  });

  @GET('$requirementService/wo/site-survey/v1/detail/{code}')
  Future<ActWoDetail> getDetailAssociationWo({
    @Path('code') required String code,
  });

  @GET('$requirementService/wo/site-survey/v1/search')
  Future<ArrayResponse<ActWoDetail>> getListWoSurvey({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? size,
    @Query('from') String? from,
    @Query('to') String? to,
    @Query('processStatus') String? processStatus,
    @Query('status') String? status,
  });

  @GET('$requirementService/association/v1/aggregate-status')
  Future getAssociationStatus({
    @Query('keyword') String? keyword,
    @Query('from') String? from,
    @Query('to') String? to,
    @Query('demandCode') List<String>? demandCode,
    @Query('processStatus') List<String>? processStatus,
    @Query('types') List<String>? types,
  });

  @PUT('$requirementService/association/v1/insert-demand')
  Future insertProductService({
    @Body() required SaveActProductServiceBody body,
  });

  @PUT('$requirementService/wo/site-survey/v1/start/{code}')
  Future startSiteWOSurvey({
    @Path('code') required String code,
  });

  @GET('$requirementService/config/v1/detail')
  Future<ConfigEntity?> getConfigOverdue({
    @Query('code') String? code,
  });

  @GET('$requirementService/solar/v1/quote')
  Future<SolarEnergyEntity?> getInfoSolarEnergy({
    @Body() required SolarEnergyBody body,
  });

  @GET('$requirementService/solar/v1/compare')
  Future<List<SolarEnergyEntity>?> compareSolarEnergy({
    @Body() required SolarEnergyBody body,
  });

  @POST('$requirementService/solar/v1/save')
  Future saveSolarEnergy({
    @Body() required SaveSolarEnergyBody body,
  });

  @POST('$requirementService/solar/v1/get-file')
  Future<Base64Entity?> downloadSolarEnergy({
    @Body() required SaveSolarEnergyBody body,
  });

  @PUT("$requirementService/association/v1/confirm-call")
  Future updateAssociationCall({
    @Query('associationCode') required String associationCode,
  });

  @POST("$aioService/service/order/b2c/doSearchCurrentUserOKOrder")
  Future<ArrayResponse<AioCollectionInfoEntity>?> searchCollectionInfo({
    @Body() required AioCollectionInfoEntity body,
  });

  @POST("$aioService/service/aioSurveyRsService/b2c/getAutoCompleteSurvey")
  Future<ArrayResponse<AioSurveyEntity>?> getAutoCompleteSurvey({
    @Body() required AioSurveyEntity body,
  });

  @POST(
      "$aioService/service/aioSurveyRsService/b2c/getListCompletedSurveyCustomer")
  Future<ArrayResponse<AioSurveyCustomerEntity>?>
      getAutoCompleteSurveyCustomer({
    @Body() required AioSurveyCustomerEntity body,
  });

  @POST("$aioService/service/AIOContractRestService/service/findDropDownData")
  Future<ArrayResponse<DropDownListEntity>?> findDropDownData({
    @Body() required DropDownListEntity body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/b2c/getDataPackageDetail")
  Future<ListDataAioPackageResponse<AioPackageEntity>?> getDataPackageDetail({
    @Body() required AioPackageParamEntity? body,
  });

  @POST("$aioService/service/AIOContractRestService/service/getServiceHs")
  Future<ArrayResponse<AioServiceEntity>?> getServiceHs({
    @Body() required AioServiceParamEntity body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/getCodeAioConfigService")
  Future<AioConfigEntity?> getCodeAioConfigService({
    @Body() required InternalStaffEntity body,
  });

  @POST("$aioService/service/aioContactRequestWsService/b2c/getListGoodsType")
  Future<ArrayResponse<DropDownListEntity>?> getListGoodsType({
    @Body() DropDownListEntity? body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/b2c/saveNewContract")
  Future<AioContractResponseEntity?> saveNewContract({
    @Body() required AioContractParam body,
  });

  @POST(
      "$aioService/service/AIOContractSellRestService/service/b2c/getStaffByEmployeeCode")
  Future<ArrayResponse<InternalStaffEntity>?> getStaffByEmployeeCode({
    @Body() AioCollectionInfoEntity? body,
  });

  @POST(
      "$aioService/service/AIOStockTransRestService/service/b2c/getListUserStock")
  Future<ArrayResponse<InternalStaffEntity>?> getListUserStock({
    @Body() InternalStaffEntity? body,
  });

  @POST(
      "$aioService/service/signVofficeRsService/b2c/getForAutoCompleteSysUser")
  Future<ArrayResponse<InternalStaffEntity>?> getForAutoCompleteSysUser({
    @Body() InternalStaffEntity? body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/b2c/findListTotalContract")
  Future<ArrayResponse<AioContractEntity>?> findListTotalContract({
    @Body() required AioContractEntity body,
  });

  @POST(
      "$aioService/service/AIOContractSellRestService/service/b2c/getListContractDetailByContractId")
  Future<ArrayResponse<AioContractDetailEntity>?>
      getListContractDetailByContractId({
    @Body() required AioContractEntity body,
  });

  @POST(
      "$aioService/service/AIOContractSellRestService/service/b2c/getListContractSellTaskDetail")
  Future<ArrayResponse<AioContractDetailEntity>?>
      getListContractSellTaskDetail({
    @Body() required AioContractParam body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/b2c/getListPackageGood")
  Future<ArrayResponse<AioPackageEntity>?> getListPackageGood({
    @Body() required AioContractParam body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/getNewCommissionRate")
  Future<AioCommissionRateEntity?> getNewCommissionRate({
    @Body() required AioContractParam body,
  });

  @POST("$aioService/service/AIOContractRestService/service/getDataPackage")
  Future<ArrayResponse<AioInvoiceItemEntity>?> getListInvoiceItem({
    @Body() required AioInvoiceItemEntity param,
  });

  @POST("$aioService/service/aioOnlineSurveyRsService/getOrderRequest")
  Future<ArrayResponse<AioInvoiceItemEntity>?> getOrderRequest({
    @Body() required AioContractEntity param,
  });

  @POST("$aioService/service/orderRequest/b2c/dataOrderRequestForContract")
  Future<ArrayResponse<AioInvoiceItemEntity>?> dataOrderRequestForContract({
    @Body() required AioContractEntity param,
  });

  @GET(
      "$aioService/service/AIOContractRestService/service/b2c/getReasonOutOfDate")
  Future<ArrayResponse<String>?> getReasonExpired();

  @POST(
      "$aioService/service/AIOContractRestService/service/b2c/updateContractHold")
  Future<AioContractResponseEntity?> updateContractHold({
    @Body() required AioContractParam body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/startContractService")
  Future<AioContractResponseEntity?> startContractService({
    @Body() required AioContractParam body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/b2c/getListAppParamByParType")
  Future<ArrayResponse<DropDownListEntity>?> getListAppParamByParType({
    @Body() DropDownListEntity? body,
  });

  @POST(
      "$aioService/service/aioCancelOrderWsRsService/passToken/createCancelOrder")
  Future<AioContractResponseEntity?> createCancelOrder({
    @Body() AioRequestCancelBody? body,
  });

  @POST(
      "$aioService/service/aioCancelOrderWsRsService/passToken/deleteCancelOrder")
  Future<AioContractResponseEntity?> deleteCancelOrder({
    @Body() AioRequestCancelBody? body,
  });

  @POST(
      "$aioService/service/AIOContractSellRestService/service/b2c/getGoodsPersonalWarehouse")
  Future<ArrayResponse<AioSupplyEntity>?> getGoodsPersonalWarehouse({
    @Body() AioContractParam? body,
  });

  @POST(
      "$aioService/service/AIOStockTransRestService/service/b2c/getQuantityInstockTtqh")
  Future<ArrayResponse<AioSupplyEntity>?> getQuantityInstockTtqh({
    @Body() AioContractParam? body,
  });

  @POST(
      "$aioService/service/AIOContractSellRestService/service/b2c/getGoodsProvinceWarehouse")
  Future<ArrayResponse<AioSupplyEntity>?> getGoodsProvinceWarehouse({
    @Body() AioContractParam? body,
  });

  @POST("$aioService/service/AIOStockTransRestService/service/b2c/getListStock")
  Future<ListStockEntity?> getListStockAIO({
    @Body() SysUserRequest? body,
  });

  @POST("$aioService/service/AIOStockTransRestService/service/b2c/getListGood")
  Future<ListGoodEntity?> getListGoodAio({
    @Body() AioContractEntity? body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/updatePayTypeContract")
  Future<AioContractResponseEntity?> updatePayTypeContract({
    @Body() AioContractParam? body,
  });

  @POST("$aioService/service/aioInvoiceRsService/b2c/getDataForInvoice")
  Future<AioInvoiceResponseEntity?> getDataForInvoice({
    @Body() AioContractEntity? body,
  });

  @POST("$aioService/service/aioInvoiceRsService/b2c/saveInvoice")
  Future<AioInvoiceResponseEntity?> saveInvoice({
    @Body() AioInvoiceEntity? body,
  });

  @POST(
      "$aioService/service/aioSignDigitalContractWsRsService/b2c/findDigitalContractPath")
  Future<AioInvoiceResponseEntity?> findDigitalContractPath({
    @Body() AioContractDigitalParam? body,
  });

  @POST(
      "$aioService/service/aioSignDigitalContractWsRsService/b2c/signDigitalContract")
  Future<AioInvoiceResponseEntity?> signDigitalContract({
    @Body() AioContractDigitalParam? body,
  });

  @POST("$aioService/service/payment/getParams")
  Future<AioContractResponseEntity?> getUrlToRequestSubmitMoney({
    @Body() AioContractDigitalParam? body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/updateStatusAndPerformerContract")
  Future<AioContractResponseEntity?> updateStatusAndPerformerContract({
    @Body() AioContractEntity? param,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/b2c/getFullContractInfo")
  Future<AioContractInfoEntity?> getFullContractInfo({
    @Body() AioContractEntity? param,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/b2c/v2/getFullContractInfo")
  Future<AioContractInfoEntity?> getDataByCode({
    @Body() AioContractEntity? param,
  });

  @POST(
      "$aioService/service/AIOContractManageRestService/service/b2c/getContractOfPerformer")
  Future<ArrayResponse<AioContractEntity>?> getContractOfPerformer({
    @Body() required AioContractParam body,
  });

  @POST("$aioService/service/AIOContractRestService/service/edit")
  Future<AioContractResponseEntity?> editContractOfPerformer({
    @Body() required AioContractUpdateParam body,
  });

  @POST("$aioService/service/AIOContractRestService/service/getTransportMethod")
  Future<AioMethodResponseEntity?> getTransportMethod({
    @Body() required AioContractUpdateParam body,
  });

  @POST(
      "$aioService/service/AIOContractSellRestService/service/b2c/checkEligibleToApply")
  Future<AioContractResponseEntity?> checkEligibleToApply({
    @Body() required AioContractParam body,
  });

  @POST(
      "$aioService/service/AIOContractSellRestService/service/b2c/endContractSell")
  Future<AioContractResponseEntity?> endContractSell({
    @Body() required AioContractDigitalParam body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/b2c/checkDeploymentMethod")
  Future<AioContractResponseEntity?> checkDeploymentMethod({
    @Body() required AioCheckDeployment body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/getLogInventoryContract")
  Future<AioLogInventoryResponse?> getLogInventoryContract({
    @Body() required AioContractParam body,
  });

  @GET("$orderService/alerts/v1/pricing-package-orders")
  Future<dynamic> getPricingPackageOrdersAlert({
    @Query("alertDeadlineType") String? alertDeadlineType,
  });

  @POST("$aioService/service/AIOContractRestService/service/updateStatusActual")
  Future<AioContractResponseEntity?> updateStatusActual({
    @Body() required List<AioContractActual> body,
  });

  @POST("$aioService/service/AIOContractRestService/service/getStatusActual")
  Future<AioContractResponseActualEntity?> getStatusActual({
    @Body() required AioContractParam body,
  });

  @POST(
      "$aioService/service/AIOContractSellRestService/service/b2c/calculatorCostTechnicalBranch")
  Future<AioContractResponseEntity?> calculatorCostTechnicalBranch({
    @Body() required AioContractParam? body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/validOtpSignContract")
  Future<AioContractResponseEntity?> validOtpSignContract({
    @Body() required AioContractEntity? body,
  });

  @POST("$aioService/service/aioSignDigitalContractWsRsService/b2c/sendSmsOtp")
  Future<AioContractResponseEntity?> sendSmsOtp({
    @Body() required AioContractEntity? body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/validOtpSignContract")
  Future<AioContractResponseEntity?> validOTPSignContract({
    @Body() required AioContractEntity? body,
  });

  @POST(
      "$aioService/service/AIOContractRestService/service/getListAppParamSolarCare")
  Future<ArrayResponse<AppParam>?> getListAppParamSolarCare({
    @Body() required AioContractEntity? body,
  });

  @POST(
      "$aioService/service/AIOLookupServiceWsRsService/service/b2c/getListContractLookupServiceTask")
  Future<AioArrayResponse<ContractWarrantyEntity>?> getListContractWarranty({
    @Body() required SearchWarrantyBody body,
  });

  @POST(
      "$aioService/service/AIOLookupServiceWsRsService/service/b2c/getViewLookDetail")
  Future<AioDataResponse<ContractWarrantyEntity>?> getDetailContractWarranty({
    @Body() required SearchWarrantyBody body,
  });

  @POST(
      "$aioService/service/AIOLookupServiceWsRsService/service/b2c/getListWarrantyStation")
  Future<AioArrayResponse<WarrantyCenterEntity>?> getListWarrantyCenter({
    @Body() required SearchWarrantyBody body,
  });

  @GET('$ticketService/error/v1/group')
  Future<List<GroupError>?> getGroupError({
    @Query('keyword') String? keyword,
  });

  @POST('$ticketService/error/v1/upload')
  Future<FileReportError?> uploadFileError({
    @Body() required FormData body,
  });

  @POST('$ticketService/error/v1')
  Future createReportError({
    @Body() required ReportErrorBody body,
  });

  @PUT('$ticketService/error/v1')
  Future editReportError({
    @Body() required ReportErrorBody body,
  });

  @GET('$ticketService/error/v1')
  Future<ArrayResponse<ReportErrorEntity>?> getListReportError({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('fromDate') String? fromDate,
    @Query('toDate') String? toDate,
    @Query('status') List<String>? status,
    @Query('groupErrorIds') List<int>? groupErrorIds,
  });

  @POST('$ticketService/error/v2')
  Future createReportErrorWithoutAuth({
    @Body() required ReportErrorBody body,
  });

  @GET('$ticketService/error/v1/{id}')
  Future<ReportErrorEntity> getDetailReportError({
    @Path('id') required int id,
  });

  @GET('$ticketService/error/v1')
  Future<ArrayResponse<ReportErrorEntity>?> getListDuplicateErrors({
    @Query('groupErrorId') required String groupErrorId,
    @Query('listNotStatus') List<String>? listNotStatus,
  });

  @GET('$ticketService/error/v1/duplicate-in-group')
  Future<ArrayResponse<ReportErrorEntity>?> getListDuplicateInGroup({
    @Query('groupErrorId') required String groupErrorId,
    @Query('username') required String username,
  });

  @GET('$ticketService/error/v1/instruction')
  Future<ArrayResponse<InstructionEntity>?> getListInstructions();

  @POST('$ticketService/error/v1/feedback')
  Future createFeedbackError({
    @Body() required FeedbackErrorBody body,
  });

  // giải trình
  @POST("$cimService/starter/v1/explanation/list-explanation-for-mobile")
  Future<ArrayResponseCIM<ExplanationEntity>?> explanationList({
    @Body() required ExplanationBody? body,
  });

  @POST("$cimService/starter/v1/explanation/get-detail-explanation")
  Future<DataResponse<ExplanationDetailEntity>?> getDetailExplanation({
    @Body() required ExplanationDetailBody? body,
  });

  @POST("$cimService/starter/v1/explanation/get-explanation-history")
  Future<ArrayResponseCIM<ExplanationHistoryEntity>?> getHistoryExplanation({
    @Body() required ExplanationHistoryBody? body,
  });

  @POST("$cimService/starter/v1/common/find-appparam-partype-module")
  Future<ArrayResponseCIM<ExplanationReasonEntity>?> getReasonsExplanation({
    @Body() required ExplanationReasonsBody? body,
  });

  @POST("$cimService/starter/v1/sys-user/find-all-user")
  Future<DataResponse<ArrayResponseCIM<ExplanationEmployeeEntity>>?>
      getEmployeeExplanation({
    @Body() required ExplanationEmployeeBody? body,
  });

  @GET("$cimService/starter/v1/explanation/penalty/user?explanationId={id}")
  Future<ArrayResponseCIM<PersonnelPenaltyEntity>?> getPersonnelPenalty({
    @Path('id') required int id,
  });

  @POST("$cimService/starter/v1/explanation/explanation-by-source")
  Future<DataResponse<ExplanationEmployeeEntity>?> updateExplanation({
    @Body() required ExplanationUpdateBody? body,
  });

  @POST("$cimService/starter/v1/explanation/penalty/create-mobile")
  Future<DataResponse?> createPenaltyExplanation({
    @Body() required ExplanationPenaltyBody? body,
  });

  @POST("$cimService/starter/v1/explanation/get-total-fine")
  Future<DataResponse<double>?> getTotalPenalty({
    @Body() required TotalPenaltyBody? body,
  });

  @POST('$resourceService/file/v2/upload')
  Future<List<ExplanationFileEntity>?> uploadImageV2({
    @Body() required FormData body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/getListTicketForB2C')
  Future<ArrayResponseCIM<ComplainEntity>?> getListComplain({
    @Body() required ComplainBody complainBody,
  });

  @GET('$cimService/starter/v1/ticket-system/get-list-category/B2C')
  Future<ArrayResponseCIM<DropDownComplainEntity>?> getListCategory();

  @POST('$cimService/starter/v1/ticket-system/get-list-product')
  Future<ArrayResponseCIM<DropDownComplainEntity>?> getProductByCategoryId({
    @Body() required ComplainBody complainBody,
  });

  @POST('$cimService/starter/v1/ticket-system/get-list-complaint-group')
  Future<ArrayResponseCIM<DropDownComplainEntity>?> getCreateListComplain({
    @Body() required ComplainBody complainBody,
  });

  @POST('$cimService/starter/v1/ticket-system/get-list-complaint-type')
  Future<ArrayResponseCIM<DropDownComplainEntity>?> getTypeComplain({
    @Body() required ComplainBody complainBody,
  });

  @POST('$cimService/starter/v1/ticket-system/checkContractByCode')
  Future<DataResponse<CreateContractInfoEntity>?> checkContractByCode({
    @Body() required ContractComplainBody contractComplainBody,
  });

  @POST('$cimService/starter/v1/ticket-system/create-ticket-by-source')
  Future<DataResponse<ContractComplainEntity>?> createComplain({
    @Body() required CreateComplainBody createComplainBody,
  });

  @POST('$cimService/starter/v1/ticket-b2c/detailTicketForB2C')
  Future<DataResponse<DetailComplainEntity>?> getDetailComplain({
    @Body() required ComplainBody complainBody,
  });

  @POST('$cimService/starter/v1/common/find-param-code-partype-module')
  Future<ArrayResponseCIM<DropDownComplainEntity>?> getReasonGroup({
    @Body() required ReasonGroupBody reasonGroupBody,
  });

  @POST('$cimService/starter/v1/common/find-appparam-partype-module')
  Future<ArrayResponseCIM<DropDownComplainEntity>?> getProcessSymbol({
    @Body() required ReasonGroupBody reasonGroupBody,
  });

  @POST('$aioService/service/reglect/getInfoContractBySerial')
  Future<CheckSerialEntity?> getContractBySerial({
    @Body() required CheckSerialBody checkSerial,
  });

  @POST('$cimService/starter/v1/sys-user/get-all')
  Future<DataResponse<ComplainStaffEntity>?> getStaffComplain({
    @Body() required ComplainBody complainBody,
  });

  @POST('$cimService/starter/v1/ticket-b2c/syncTicketFromB2C')
  Future<DataResponse?> assignComplain({
    @Body() required AssignComplainBody body,
  });

  @POST('$aioService/service/reglect/b2c/getListSerial')
  Future<CheckSerialEntity?> getSerialContractDetail({
    @Body() required SelectedSerialComplainBody body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/acceptInteract')
  Future<DataResponse<SignInteractComplainEntity>?> signContractWorkComplain({
    @Body() required SignWorkContractComplainBody body,
  });

  @POST('$cimService/starter/v1/ticket-system/get-interact-history')
  Future<DataResponse<List<ComplainImpactHistoryEntity>>?> getInteractHistory({
    @Body() required InteractHistoryBody body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/getListTicketHistory')
  Future<DataResponse<List<ComplainReflectHistoryEntity>>?>
      getReflectTicketHistory({
    @Body() required ComplainReflectHistoryBody body,
  });

  @POST('$cimService/starter/v1/mobile-call-his/insertLogMobileCall')
  Future<DataResponse<MobileCallEntity>> insertLogMobileCall({
    @Body() required InsertMobileCallBody body,
  });

  @POST('$cimService/starter/v1/ticket-system/assignTicketByPerformer')
  Future<DataResponse<RefuseTicketEntity>> refuseTicket({
    @Body() required RefuseTicketBody body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/syncTicketFromB2C')
  Future<DataResponse> complainExtension({
    @Body() required ComplainExtensionBody body,
  });

  @POST('$cimService/starter/v1/common/find-appparam-type-partype-module')
  Future<DataResponse<List<ComplainReasonExtendEntity>>> getComplainExtension({
    @Body() required ComplainReasonExtendBody body,
  });

  @POST('$cimService/starter/v1/mobile-call-his/logMobileCall')
  Future<DataResponse<List<ComplainHistoryMobileCallEntity>>>
      getComplainHistoryMobileCall({
    @Body() required ComplainHistoryMobileCallBody body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/ticketCreateOTP')
  Future<DataResponse?> ticketCreateOtp({
    @Body() required SendOtpComplainBody body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/performerSign')
  Future<DataResponse?> performerSign({
    @Body() required SignComplainBody body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/customerSign')
  Future<DataResponse?> customerSign({
    @Body() required SignComplainBody body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/genBBNTTicket')
  Future<DataResponse<SignInteractComplainEntity>?>
      signContractAcceptanceComplain({
    @Body() required SignAcceptanceContractBody body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/syncTicketFromB2C')
  Future<DataResponse<FileSignComplainEntity>?> completeComplain({
    @Body() required SignAcceptanceContractBody body,
  });

  @POST('$resourceService/file/v2/upload')
  Future<List<FileV2Entity>?> uploadFileV2({
    @Body() required FormData body,
  });

  @POST('$cimService/starter/v1/ticket-system/create-interact')
  Future<DataResponse?> createInteractHistory({
    @Body() required CreateInteractHistoryBody body,
  });

  @POST('$cimService/starter/v1/ticket-b2c/syncTicketFromB2C')
  Future<DataResponse?> updateTicketInfo({
    @Body() required UpdateTicketInfoBody body,
  });

  @GET('$userProfileService/sale-groups/v1/search')
  Future<ArrayResponse<SaleTeamInfoEntity>?> getListSaleTeam({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @POST('$userProfileService/sale-groups/v1')
  Future createMyTeam();

  @GET('$userProfileService/sale-groups/v1/{groupCode}/join-requests')
  Future<ArrayResponse<UserSimpleEntity>?> getListMemberRequest({
    @Path('groupCode') String? groupCode,
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @GET('$userProfileService/sale-groups/v1')
  Future<SaleTeamInfoEntity?> getTeamInfo();

  @DELETE('$userProfileService/sale-groups/v1/{teamId}')
  Future<SaleTeamInfoEntity?> deleteTeam({
    @Path('teamId') String? teamId,
  });

  @PUT('$userProfileService/sale-groups/v1/{teamId}/leave')
  Future<SaleTeamInfoEntity?> outTeam({
    @Path('teamId') String? teamId,
  });

  @GET('$userProfileService/sale-groups/v1/members')
  Future<ArrayResponse<UserSimpleEntity>?> getMembersTeam({
    @Query('groupCode') String? groupCode,
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @PUT('$userProfileService/sale-groups/v1/members')
  Future updateMemberInfo({
    @Body() required UpdateSaleMemberBody body,
  });

  @GET('$userProfileService/sale-groups/v1/{groupCode}/field-types')
  Future<List<CodeEntity>?> getFieldsLeader({
    @Path('groupCode') String? groupCode,
  });

  @GET('$userProfileService/sale-groups/v1/members/{username}')
  Future<SaleUserInfoEntity?> getMemberInfo({
    @Path('username') String? username,
  });

  // @GET('$userProfileService/sale-groups/v1/job-configs')
  // Future<List<String>?> getJobsSaleGroup({
  //   @Query("keyword") String? keyword,
  // });

  @POST('$userProfileService/sale-groups/v1/members/validate-phone')
  Future<SaleUserInfoEntity?> verifyPhoneNumberSalesman({
    @Body() required ValidatePhoneBody body,
  });

  @POST('$userProfileService/users/v1/ocr')
  Future<SaleUserInfoEntity?> verifyIdentitySalesman({
    @Body() required VerifyIdentityBody body,
  });

  @POST('$userProfileService/sale-groups/v1/members')
  Future addSaleMember({
    @Body() required AddSaleMemberBody body,
  });

  @PUT('$userProfileService/sale-groups/v1/members/decision')
  Future approveSaleMember({
    @Body() required ApproveSaleMemberBody body,
  });

  @POST('$userProfileService/sale-groups/v1/join-request')
  Future requestToGroup({
    @Body() required ConfirmJoinSaleTeamBody body,
  });

  @DELETE('$userProfileService/sale-groups/v1/members')
  Future deleteMember({
    @Body() required ConfirmJoinSaleTeamBody body,
  });

  @GET('$userProfileService/sale-groups/v1/group-revenue')
  Future<RevenueSaleTeamEntity?> getRevenueGroup({
    @Query("groupCode") String? groupCode,
    @Query("month") String? month,
    @Query("keyword") String? keyword,
  });

  @GET('$cimService/starter/v1/feedback/get-type-feedback')
  Future<ArrayResponseCIM<FeedbackTypeEntity>?> getListFeedbackType();

  @POST('$cimService/starter/v1/feedback/create')
  Future createFeedback({
    @Body() required CreateFeedbackBody body,
  });

  @POST('$cimService/starter/v1/feedback/get-list-feedback')
  Future<ArrayResponseCIM<FeedbackEntity>?> getListFeedback({
    @Body() required FeedbackListBody body,
  });

  @POST('$cimService/starter/v1/feedback/get-detail-feedback')
  Future<DataResponse<FeedbackEntity>?> detailFeedback({
    @Body() required dynamic body,
  });

  @POST('$cimService/starter/v1/feedBack/upload-feedBack')
  Future<FileReportError?> uploadImageFeedback({
    @Body() required FormData body,
  });

  @GET("$orderService/orders/aio/v2/{orderCode}")
  Future<OrderWarrantyEntity?> getOrderItemWarranty({
    @Path('orderCode') required String orderCode,
  });

  @GET("$orderService/orders/aio/v1/{orderCode}")
  Future<List<OrderItemWarrantyEntity>?> addOrderWarrantyItem({
    @Path('orderCode') required String orderCode,
    @Query('keyword') required String keyWord,
  });

  @POST('$ticketService/warranties/v1')
  Future<DataResponse?> getWarrantyList();

  @GET('$ticketService/warranties/v1/{code}')
  Future<DetailRequirementWarrantyEntity> getDetailWarranty({
    @Path('code') required String warrantyCode,
  });

  @GET(
      '$orderService/supplies/v1/warranty?warrantySupplyTypes={type}&keyword={key}&page={page}&size={pageSize}')
  Future<ListSuppliesEntity> getListSupplies({
    @Path('type') required String type,
    @Path('key') required String key,
    @Path('page') int? page,
    @Path('pageSize') int? pageSize,
  });

  @GET('$ticketService/wms/v1/warranty')
  Future<List<OrderItemWarrantyEntity>?> getPolicyAndImageCAT({
    @Query('orderCode') required String orderCode,
    @Query('goodIds') required List<int> goodIds,
  });

  @POST('$ticketService/warranties/v1')
  Future<CreateRequirementWarrantyEntity?> createRequirementWarranty({
    @Body() required CreateRequirementWarrantyBody body,
  });

  @GET('$ticketService/warranties/v1')
  Future<ArrayResponseCIM<WarrantyClaimEntity>?> getListWarranty({
    @Query('permissionType') String? permissionType,
    @Query('keyword') String? keyword,
    @Query('status') List<String>? status,
    @Query('startTime') DateTime? startTime,
    @Query('endTime') DateTime? endTime,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @POST('$cimService/starter/v1/sys-user/getAllUserPermission')
  Future<DataResponse<WarrantyStaffEntity>?> getStaffWarranty({
    @Body() required WarrantyStaffBody warrantyBody,
  });

  @POST('$ticketService/warranties/v1/worker-assign')
  Future assignWarranty({
    @Body() required AssignRequirementWarrantyBody body,
  });

  @POST("$ticketService/warranties/v1/refund-certificate")
  Future<SignContractEntity?> getRequirementWarrantyFileSign({
    @Body() required SignItemWarrantyBody signItemWarrantyBody,
  });

  @GET('$ticketService/warranties/v1/certificate/{code}')
  Future<SignContractEntity?> getDetailRequirementWarrantyFileSign({
    @Path('code') required String code,
  });

  @POST('$ticketService/warranties/v1/certificate/{code}')
  Future requirementWarrantyComplete({
    @Path('code') required String code,
    @Body()
    required SignCreateWarrantyCompleteBody signCreateWarrantyCompleteBody,
  });

  @POST("$ticketService/warranties/v1/confirm-refund-certificate")
  Future<DataConfirmSignEntity?> confirmSignCreateRequirementWarranty({
    @Body() required SignCreateWarrantyBody createRequirementWarrantyBody,
  });

  @POST("$userProfileService/otp/warranties/v1/send")
  Future<SignContractEntity?> sentOtpRequirementWarranty({
    @Body() required SentOtpBody body,
  });

  @POST('$userProfileService/otp/v1/verify')
  Future verifyWarrantyOTP({
    @Body() required SendOTPBody body,
  });

  @POST("$ticketService/warranties/v1/{code}/history")
  Future<ArrayResponseCIM<HistoryWarrantyEntity>?> getHistoryWarrantyV1({
    @Path('code') String? code,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Body() required HistoryWarrantyBody body,
  });

  @POST("$ticketService/warranties/v2/{code}/history")
  Future<ArrayResponseCIM<HistoryWarrantyEntity>?> getHistoryWarrantyV2({
    @Path('code') String? code,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @GET("$resourceService/mobile-call-log/v1/list")
  Future<ArrayResponseCIM<CallHistoryEntity>?> getHistoryCalling({
    @Query('referenceId') String? referenceId,
    @Query('functional') String? functional,
    @Query('types') List<String>? types,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @POST('$ticketService/warranties/v1/{warrantyCode}/send-goods')
  Future sendGoods({
    @Path('warrantyCode') required String warrantyCode,
    @Body() required WarrantyGoodsBody body,
  });

  @POST('$ticketService/warranties/v1/{warrantyCode}/receive-goods')
  Future receiveGoods({
    @Path('warrantyCode') required String warrantyCode,
    @Body() required WarrantyGoodsBody body,
  });

  @POST('$cimService/starter/v1/common/find-appparam-partype-module')
  Future<ArrayResponseCIM<ReasonAppointmentEntity>?>
      getReasonAppointmentSuggestion({
    @Body() required ReasonAppointmentSuggestionBody body,
  });

  @POST('$ticketService/proposes/warranties/v1/schedule/{code}')
  Future scheduleAppointment({
    @Path('code') required String code,
    @Body() required ScheduleAppointmentBody body,
  });

  @DELETE('$ticketService/proposes/warranties/v1/schedule/{code}')
  Future cancelAppointment({
    @Path('code') required String code,
  });

  @PUT('$ticketService/warranties/v1/{warrantyCode}')
  Future updateRequirementWarranty({
    @Path('warrantyCode') required String warrantyCode,
    @Body() required UpdateDetailRequirementWarrantyBody body,
  });

  @POST('$ticketService/warranties/v1/{warrantyCode}/supplies')
  Future suppliesWarrantyRequest({
    @Path('warrantyCode') required String warrantyCode,
    @Body() required SuppliesWarrantyRequestBody body,
  });

  @POST('$ticketService/warranties/v1/{warrantyCode}/export-supplies')
  Future exportSuppliesWarrantyRequest({
    @Path('warrantyCode') required String warrantyCode,
    @Body() required ExportSuppliesWarrantyBody body,
  });

  @POST('$ticketService/warranties/v1/{warrantyCode}/cancel-export-supplies')
  Future cancelExportSuppliesWarrantyRequest({
    @Path('warrantyCode') required String warrantyCode,
  });

  @POST('$userProfileServiceAioService/service/collab/getPortraitList')
  Future<AgencyEntity?> getPortraitList({
    @Body() required PortraitBody body,
  });

  @POST('$userProfileServiceAioService/service/collab/getSourceAgency')
  Future<AgencyEntity?> getSourceAgency();

  @POST('$userProfileServiceAioService/service/collab/getListBank')
  Future<BankEntity?> getListBank({
    @Body() required BankBody body,
  });

  @POST(
      '$userProfileServiceAioService/service/aioConfigLevelCodeWsService/getListLevelCode')
  Future<RegisterGoodsEntity?> getListLevelCode({
    @Body() required GoodsBody body,
  });

  @GET('$userProfileServiceAioService/service/collab/getListOccupation')
  Future<RegisterGoodsEntity?> getListOccupation();

  @POST('$userProfileServiceAioService/service/collab/getSysGroupTree')
  Future<BranchEntity?> getListManagementUnit({
    @Body() required ManagementUnitBody body,
  });

  @POST('$userProfileServiceAioService/service/collab/b2c/saveFileToCheckAI')
  Future<UpdateImageEntity?> updateImageRegister({
    @Body() required RegisterBody body,
  });

  @POST('$userProfileServiceAioService/service/collab/getContractCode')
  Future<ContractCodeEntity?> getContractCode({
    @Body() required ContractBody body,
  });

  @POST(
      '$userProfileServiceAioService/service/collab/b2c/getFilePathContractCTV')
  Future<SignContractRegisterEntity?> getRegisterFileSign({
    @Body() required RegisterBody body,
  });

  @POST('$userProfileServiceAioService/service/collab/sendSmsOtpCtv')
  Future<OtpRegisterEntity?> sendOtpRegister({
    @Body() required RegisterBody body,
  });

  @POST('$userProfileServiceAioService/service/collab/validOtpContractCtv')
  Future<OtpRegisterEntity> verifyOTPRegister({
    @Body() required RegisterBody body,
  });

  @POST('$userProfileServiceAioService/service/collab/b2c/getList')
  Future<CollaboratorEntity?> getCollaborators({
    @Body() required RequestCollaboratorBody body,
  });

  @POST('$userProfileServiceAioService/service/collab/b2c/detail')
  Future<DetailCollaboratorEntity?> getDetailCollaborators({
    @Body() required DetailCollaboratorBody body,
  });

  // order agency
  @POST('$aioService/service/orderRequest/b2c/getInforLevelCategoryForOrder')
  Future<OrderRequestCartEntity> getInfoLevelCategoryForOrder({
    @Body() required OrderRequestCartBody body,
  });

  @POST('$aioService/service/orderRequest/b2c/saveOrderRequest')
  Future<SaveOrderAgencyEntity> createOrderAgencyRequest({
    @Body() required OrderRequestCartBody body,
  });

  @POST('$aioService/service/orderRequest/b2c/getListOrders')
  Future<WorkAgencyEntity> getWorkAgency({
    @Body() required WorkAgencyBody body,
  });

  @POST('$aioService/service/orderRequest/b2c/getOrderDetail')
  Future<DealerOrderDetailEntity> getDealerOrderDetail({
    @Body() required DealerOrderDetailBody body,
  });

  @POST('$aioService/service/orderRequest/b2c/updateStatusOrderAgency')
  Future<DealerOrderDetailEntity> cancelOrderAgent({
    @Body() required CancelOrderAgentBody body,
  });

  @POST('$aioService/service/orderRequest/b2c/genContractAgency')
  Future<DealerOrderDetailEntity> confirmIssueNote({
    @Body() required ConfirmIssueNoteBody body,
  });

  @POST('$aioService/service/orderRequest/b2c/getListTransExport')
  Future<TransExportEntity> getListTransExport({
    @Body() required SynStockTransBody body,
  });

  @POST('$aioService/service/orderRequest/b2c/refuseStockTrans')
  Future<DealerOrderDetailEntity> rejectIssueNote({
    @Body() required RejectIssueNoteBody body,
  });

  @POST(
      '$aioService/service/orderRequest/b2c/acceptOrRejectOrderRequestEmployee')
  Future<DealerOrderDetailEntity> rejectOrderAgent({
    @Body() required CancelOrderAgentBody body,
  });

  @POST(
      '$aioService/service/orderRequest/b2c/acceptOrRejectOrderRequestEmployee')
  Future<DealerOrderDetailEntity> approvedOrderAgent({
    @Body() required ApprovedOrderAgencyBody body,
  });

  @POST('$aioService/service/aioInvoiceRsService/b2c/getDataForInvoice')
  Future<InvoiceInfoEntity> getInvoiceData({
    @Body() required InvoiceInfoBody body,
  });

  @POST('$aioService/service/aioInvoiceRsService/b2c/getFilePathInvoice')
  Future<InvoiceInfoFileEntity> getInvoicePdf({
    @Body() required InvoiceInfoFileBody body,
  });

  @POST('$aioService/service/aioInvoiceRsService/b2c/saveInvoice')
  Future<AioInvoiceResponseEntity> updateInvoice({
    @Body() required InvoiceSaveInfoBody body,
  });

  @POST(
      '$aioService/service/aioSignDigitalContractWsRsService/b2c/findContractPath')
  Future<SignContractInvoiceEntity> findContractPathInvoice({
    @Body() required SignContractInvoiceBody body,
  });

  @POST(
      '$aioService/service/aioSignDigitalContractWsRsService/b2c/signContract')
  Future<SignContractInvoiceEntity> signContractInvoice({
    @Body() required SignContractInvoiceBody body,
  });

  @POST('$aioService/service/orderRequest/b2c/getDataAddOrder')
  Future<RequestOrderEntity> getDataAddOrder({
    @Body() required RequestOrderBody body,
  });

  @POST('$aioService/service/orderRequest/b2c/findAgencyByUserCompany')
  Future<AgencyUserCompanyEntity> getAgencyByUserCompany({
    @Body() required AgencyUserCompanyBody body,
  });

  @POST('$aioService/service/payment/getParams')
  Future<SignContractInvoiceEntity> createViettelPayQRCodeAgency({
    @Body() required CreatePaymentBody body,
  });

  @POST('$aioService/service/payment/getQrOnePay')
  Future<SignContractInvoiceEntity> createOnePayQRCodeAgency({
    @Body() required CreatePaymentBody body,
  });

  @POST('$orderService/agent/cart/v1')
  Future saveOrderAgencyDraft({
    @Body() required OrderAgencyDraftBody body,
  });

  @GET('$orderService/agent/cart/v1')
  Future<OrderAgencyDraftEntity> getListProductInCart();

  @POST(
      '$aioService/service/aioCancelOrderWsRsService/b2c/passToken/createCancelOrder')
  Future<DealerOrderDetailEntity> requestCancelAgency({
    @Body() required RequestCancelOrderAgencyBody body,
  });

  @POST(
      '$aioService/service/aioCancelOrderWsRsService/b2c/passToken/getListOrderOrContract')
  Future<DuplicateOrderAgencyEntity> getOrderAgencyDuplicates({
    @Body() DuplicateOrderAgencyBody? body,
  });

  @POST('$aioService/service/orderRequest/b2c/getLog')
  Future<HistoryOrderAgencyEntity> getHistoryOrderAgency({
    @Body() HistoryOrderAgencyBody? body,
  });

  @GET("$orderService/quotes/v1")
  Future<CustomerQuoteEntity> getListCustomerQuote({
    @Query('keyword') String? keyword,
    @Query('quoteStatus') List<String>? quoteStatus,
    @Query('startTime') String? startTime,
    @Query('endTime') String? endTime,
    @Query('size') int? size,
    @Query('page') int? page,
    @Query('sort') String? sort,
  });

  @GET("$orderService/quotes/v1/{productCode}")
  Future<CustomerQuoteDetailEntity> getDetailCustomerQuote({
    @Path('productCode') String? productCode,
  });

  @PUT('$orderService/quotes/v1/{quoteCode}/approval')
  Future updateCustomerQuoteStatus({
    @Path('quoteCode') String? productCode,
    @Body() required UpdateCustomerQuoteBody body,
  });

  @PUT('$orderService/quotes/v1/{quoteCode}')
  Future updateDetailCustomerQuote({
    @Path('quoteCode') String? productCode,
    @Body() UpdateDetailCustomerQuoteBody? body,
  });

  @POST('$orderService/quotes/v1')
  Future createCustomerQuote({
    @Body() NewCustomerQuoteBody? body,
  });

  @POST('$aioService/service/goodService/b2c/goods/search')
  Future<List<QuoteProductsEntity>> getListProductCustomerQuote({
    @Body() QuoteProductsBody? body,
    @Header("x-api-key") String? apiKey,
  });

  @POST("$cimService/starter/v1/customer/dashboard/csat-chart")
  Future<ArrayResponseCIM<CsatChartDto>?> getCsatChart({
    @Body() required CustomerRatingBody? body,
  });

  @POST("$cimService/starter/v1/customer/dashboard/csat-list")
  Future<ArrayResponseCIM<FeedbackDto>?> getCustomerFeedback({
    @Body() required CustomerRatingBody? body,
  });

  @GET('$userProfileService/users/v1')
  Future<ListApproveLeaderEntity?> getApproveLeader({
    @Query('keyword') String? keyWord,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @POST('$userProfileService/otp/v1/verify')
  Future verifyOtp({
    @Body() required VerifyDraftContractBody body,
  });

  @POST('$userProfileService/otp/draft-contract/v1/send')
  Future sendOtpDraftContract({
    @Body() required SignAcceptanceContractBody body,
  });

  // orderservice draft contract

  @GET('$contractDraftService/v1')
  Future<ArrayResponseCIM<ContractDraftEntity>?> getListContractDraft({
    @Query('keyword') String? keyword,
    @Query('draftContractStatus') String? draftContractStatus,
    @Query('startTime') DateTime? startTime,
    @Query('endTime') DateTime? endTime,
    @Query('signStartTime') DateTime? signStartTime,
    @Query('signEndTime') DateTime? signEndTime,
    @Query('page') int? page,
    @Query('size') int? pageSize,
    @Query('contractStatus') String? contractStatus,
  });

  @GET('$contractDraftService/v2/{code}')
  Future<ContractDraftDetailEntity?> getDetailContractDraft({
    @Path('code') required String code,
  });

  @GET('$orderService/material/aio/v1')
  Future<ArrayResponseCIM<ProductEntityDraftContract>?> getProducts({
    @Query('keyword') String? keyword,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @POST('$businessService/v1')
  Future onSaveBusinessPlan({
    @Body() required CreateBusinessPlanBody body,
  });

  @GET('$businessService/v1/{code}')
  Future<BusinessProposalEntity?> getDetailBusinessProposal({
    @Path('code') required String code,
  });

  @POST('$contractDraftService/v1/gen-code')
  Future<ContractDraftCodeEntity> getContractDraftCode({
    @Body() required GenContractCodeBody body,
  });

  @POST('$contractDraftService/v1/{contractCode}/submission')
  Future onSignContractDaft(
    @Path('contractCode') String contractCode,
  );

  @GET('$orderService/configure-solar-rate/aio/v1')
  Future<List<CapacityPercentEntity>> getPercentCapacity({
    @Query('keyword') String? keyword,
  });

  @POST('$contractDraftService/v1/gen-content-contract')
  Future<ContractContentEntity> getContractContent({
    @Body() required GenContractContentBody body,
  });

  @POST('$contractDraftService/v2')
  Future createContractDraft({
    @Body() required CreateDraftContractBody body,
  });

  @PUT('$contractDraftService/v2/{contractCode}')
  Future updateContractDraft({
    @Path('contractCode') String? contractCode,
    @Body() required CreateDraftContractBody body,
  });

  @GET('$reasonService/v1')
  Future<List<ReasonTypeEntity>?> getReasonType({
    @Query('typeReason') required String typeReason,
  });

  @POST('$businessService/v1/approve')
  Future approveBusinessProposal({
    @Body() required ApproveBusinessProposalBody body,
  });

  @POST('$businessService/v1/reject')
  Future rejectBusinessProposal({
    @Body() required RejectBusinessProposalBody body,
  });

  @PUT('$businessService/v1/{contractCode}')
  Future updateBusinessPlan({
    @Body() required CreateBusinessPlanBody body,
    @Path('contractCode') String? contractCode,
  });

  @POST('$businessService/v1/{draftContractCode}/sign-business-proposal')
  Future signedWhenSaved({
    @Path('draftContractCode') String? draftContractCode,
  });

  @GET('$contractDraftService/v1/preview-draft-contract/{draftContractCode}')
  Future<Base64Entity?> previewDraftContract({
    @Path('draftContractCode') required String draftContractCode,
  });

  @GET('$contractDraftService/v2/file/{fileId}')
  @DioResponseType(ResponseType.bytes)
  Future<List<int>> downloadContractFile({
    @Path('fileId') required String fileId,
  });

  @POST('$contractDraftService/v1/preview-contract-appendix')
  Future<PreviewAppendixEntity> getPreviewAppendix({
    @Body() required PreviewAppendixBody body,
  });

  @POST('$contractDraftService/v1/accept')
  Future confirmOtp({
    @Body() required SignDraftContractBody body,
  });

  @POST('$contractDraftService/v1/reject')
  Future rejectDraftContract({
    @Body() required RejectBusinessProposalBody body,
  });

  @GET('$orderService/data-authority/aio/v1')
  Future<SignUnitInfoEntity> getInfoSignUnit();

  @GET('$timekeepingService/v1')
  Future<ArrayResponseCIM<RequestTimekeepingEntity>?>
      getListRequestTimekeeping({
    @Query('statusTimeKeeping') String? statusTimeKeeping,
    @Query('startTime') DateTime? startTime,
    @Query('endTime') DateTime? endTime,
    @Query('page') int? page,
    @Query('size') int? pageSize,
  });

  @GET('$timekeepingService/v1/{id}')
  Future<DetailRequestTimekeepingEntity?> getDetailRequestTimekeeping({
    @Path('id') required String id,
  });

  @POST('$timekeepingService/v1')
  Future createRequestTimekeeping({
    @Body() required CreateOrUpdateRequestTimekeepingBody body,
  });

  @PUT('$timekeepingService/v1/{id}')
  Future updateRequestTimekeeping({
    @Path('id') required String id,
    @Body() required CreateOrUpdateRequestTimekeepingBody body,
  });

  @DELETE('$timekeepingService/v1/{id}')
  Future cancelRequestTimekeeping({
    @Path('id') required String id,
  });

  @PUT('$timekeepingService/v1/{id}/approval')
  Future approveRequestTimekeeping({
    @Path('id') required String id,
    @Body() required ApproveRequestTimekeepingBody body,
  });

  @GET('$timekeepingService/v1/schedule')
  Future<TimekeepingScheduleEntity?> getTimekeepingSchedule({
    @Query('date') required String date,
    @Query('employeeCode') String? employeeCode,
  });

  @PUT('$orderService/orders/v1/{orderCode}/self-buy-supply')
  Future buySuppliesForCustomer({
    @Path('orderCode') required String orderCode,
    @Body() required BuySuppliesForCustomerBody body,
  });
}

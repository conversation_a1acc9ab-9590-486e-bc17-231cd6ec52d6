import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:vcc/presentation/views/widgets/dialogs/network_quality_dialog.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';

enum NetworkQuality {
  excellent,  // < 100ms, success rate > 95%
  good,       // 100-300ms, success rate > 90%
  fair,       // 300-500ms, success rate > 80%
  poor,       // 500-1000ms, success rate > 70%
  bad,        // > 1000ms or success rate < 70%
  none,       // No connection
}

class NetworkQualityResult {
  final NetworkQuality quality;
  final int averageLatency;
  final double successRate;
  final String message;

  NetworkQualityResult({
    required this.quality,
    required this.averageLatency,
    required this.successRate,
    required this.message,
  });
}

class NetworkQualityMonitor {
  static final NetworkQualityMonitor _instance = NetworkQualityMonitor._internal();
  factory NetworkQualityMonitor() => _instance;
  NetworkQualityMonitor._internal();

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _qualityTestTimer;
  bool _isQualityDialogShowing = false;
  bool _isMonitoring = false;
  NetworkQuality _lastQuality = NetworkQuality.excellent;

  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    
    if (kDebugMode) {
      print('NetworkQualityMonitor: Starting quality monitoring');
    }

    // Listen to connectivity changes
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        final hasConnection = results.isNotEmpty && 
            results.any((result) => result != ConnectivityResult.none);
        
        if (hasConnection) {
          testNetworkQuality();
        } else {
          _handleNoConnection();
        }
      },
    );

    // Periodic quality check every 30 seconds
    _qualityTestTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      testNetworkQuality();
    });

    // Initial test
    testNetworkQuality();
  }

  Future<NetworkQualityResult> testNetworkQuality() async {
    if (kDebugMode) {
      print('NetworkQualityMonitor: Testing network quality...');
    }

    try {
      final results = await Connectivity().checkConnectivity();
      final hasConnection = results.isNotEmpty && 
          results.any((result) => result != ConnectivityResult.none);

      if (!hasConnection) {
        return NetworkQualityResult(
          quality: NetworkQuality.none,
          averageLatency: 0,
          successRate: 0.0,
          message: 'Không có kết nối mạng',
        );
      }

      // Test multiple hosts with timing
      final hosts = [
        'google.com',
        'cloudflare.com',
        '*******',
        'facebook.com',
        'youtube.com',
      ];

      List<int> latencies = [];
      int successCount = 0;
      int totalTests = hosts.length;

      for (String host in hosts) {
        try {
          final stopwatch = Stopwatch()..start();
          
          final result = await InternetAddress.lookup(host)
              .timeout(const Duration(seconds: 3));
          
          stopwatch.stop();
          
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            latencies.add(stopwatch.elapsedMilliseconds);
            successCount++;
            
            if (kDebugMode) {
              print('NetworkQualityMonitor: $host - ${stopwatch.elapsedMilliseconds}ms');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('NetworkQualityMonitor: $host failed - $e');
          }
        }
      }

      if (latencies.isEmpty) {
        return NetworkQualityResult(
          quality: NetworkQuality.none,
          averageLatency: 0,
          successRate: 0.0,
          message: 'Không thể kết nối internet',
        );
      }

      final averageLatency = latencies.reduce((a, b) => a + b) ~/ latencies.length;
      final successRate = (successCount / totalTests) * 100;

      final quality = _calculateQuality(averageLatency, successRate);
      final result = NetworkQualityResult(
        quality: quality,
        averageLatency: averageLatency,
        successRate: successRate,
        message: _getQualityMessage(quality, averageLatency, successRate),
      );

      if (kDebugMode) {
        print('NetworkQualityMonitor: Quality=${quality.name}, Latency=${averageLatency}ms, Success=${successRate.toStringAsFixed(1)}%');
      }

      _handleQualityResult(result);
      return result;

    } catch (e) {
      if (kDebugMode) {
        print('NetworkQualityMonitor: Error testing quality: $e');
      }
      
      return NetworkQualityResult(
        quality: NetworkQuality.none,
        averageLatency: 0,
        successRate: 0.0,
        message: 'Lỗi kiểm tra mạng',
      );
    }
  }

  NetworkQuality _calculateQuality(int latency, double successRate) {
    if (successRate < 70) return NetworkQuality.bad;
    if (latency > 1000) return NetworkQuality.bad;
    if (latency > 500 || successRate < 80) return NetworkQuality.poor;
    if (latency > 300 || successRate < 90) return NetworkQuality.fair;
    if (latency > 100 || successRate < 95) return NetworkQuality.good;
    return NetworkQuality.excellent;
  }

  String _getQualityMessage(NetworkQuality quality, int latency, double successRate) {
    switch (quality) {
      case NetworkQuality.excellent:
        return 'Mạng tuyệt vời (${latency}ms)';
      case NetworkQuality.good:
        return 'Mạng tốt (${latency}ms)';
      case NetworkQuality.fair:
        return 'Mạng khá (${latency}ms)';
      case NetworkQuality.poor:
        return 'Mạng yếu (${latency}ms) - Có thể ảnh hưởng trải nghiệm';
      case NetworkQuality.bad:
        return 'Mạng rất yếu (${latency}ms) - Khuyến nghị kiểm tra kết nối';
      case NetworkQuality.none:
        return 'Không có kết nối mạng';
    }
  }

  void _handleQualityResult(NetworkQualityResult result) {
    // Only show warning for poor/bad quality and if quality degraded
    if ((result.quality == NetworkQuality.poor || result.quality == NetworkQuality.bad) &&
        (_lastQuality == NetworkQuality.excellent || _lastQuality == NetworkQuality.good || _lastQuality == NetworkQuality.fair)) {
      _showQualityWarning(result);
    }
    
    _lastQuality = result.quality;
  }

  void _handleNoConnection() {
    final result = NetworkQualityResult(
      quality: NetworkQuality.none,
      averageLatency: 0,
      successRate: 0.0,
      message: 'Không có kết nối mạng',
    );
    
    _showQualityWarning(result);
    _lastQuality = NetworkQuality.none;
  }

  void _showQualityWarning(NetworkQualityResult result) {
    final context = ErrorDialog.navigatorKey.currentContext;
    if (context == null || _isQualityDialogShowing) return;

    _isQualityDialogShowing = true;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentContext = ErrorDialog.navigatorKey.currentContext;
      if (currentContext != null) {
        showDialog(
          context: currentContext,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return NetworkQualityDialog(
              result: result,
              onRetry: () async {
                final newResult = await testNetworkQuality();
                if (newResult.quality == NetworkQuality.excellent ||
                    newResult.quality == NetworkQuality.good ||
                    newResult.quality == NetworkQuality.fair) {
                  _hideQualityDialog();
                }
                return newResult;
              },
              onDismiss: () {
                _isQualityDialogShowing = false;
              },
            );
          },
        );
      }
    });
  }

  void _hideQualityDialog() {
    final context = ErrorDialog.navigatorKey.currentContext;
    if (!_isQualityDialogShowing || context == null) return;

    _isQualityDialogShowing = false;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentContext = ErrorDialog.navigatorKey.currentContext;
      if (currentContext != null) {
        try {
          if (Navigator.of(currentContext).canPop()) {
            Navigator.of(currentContext).pop();
          }
        } catch (e) {
          if (kDebugMode) {
            print('NetworkQualityMonitor: Error closing dialog: $e');
          }
        }
      }
    });
  }

  void stopMonitoring() {
    _isMonitoring = false;
    _connectivitySubscription?.cancel();
    _qualityTestTimer?.cancel();
    
    if (kDebugMode) {
      print('NetworkQualityMonitor: Stopped monitoring');
    }
  }

  void dispose() {
    stopMonitoring();
  }
}

import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:vcc/presentation/views/widgets/dialogs/network_dialog.dart';

class NetworkMonitor {
  static final NetworkMonitor _instance = NetworkMonitor._internal();
  factory NetworkMonitor() => _instance;
  NetworkMonitor._internal();

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _networkTestTimer;
  bool _isDialogShowing = false;
  bool _isConnected = true;
  BuildContext? _context;

  void initialize(BuildContext context) {
    _context = context;
    _startMonitoring();
  }

  void _startMonitoring() {
    // Listen to connectivity changes
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        final hasConnection = results.isNotEmpty && 
            results.any((result) => result != ConnectivityResult.none);
        
        if (hasConnection) {
          // Test actual internet connectivity
          _testInternetConnection();
        } else {
          // No connectivity at all
          _handleNoConnection();
        }
      },
    );

    // Initial check
    _checkInitialConnection();
  }

  Future<void> _checkInitialConnection() async {
    try {
      final results = await Connectivity().checkConnectivity();
      final hasConnection = results.isNotEmpty && 
          results.any((result) => result != ConnectivityResult.none);
      
      if (hasConnection) {
        _testInternetConnection();
      } else {
        _handleNoConnection();
      }
    } catch (e) {
      if (kDebugMode) {
        print('NetworkMonitor: Error checking initial connection: $e');
      }
    }
  }

  Future<void> _testInternetConnection() async {
    if (kDebugMode) {
      print('NetworkMonitor: Testing internet connection...');
    }

    try {
      // Test with multiple reliable hosts
      final hosts = [
        'google.com',
        'cloudflare.com',
        '*******',
      ];

      bool hasInternet = false;

      for (String host in hosts) {
        try {
          if (kDebugMode) {
            print('NetworkMonitor: Testing host: $host');
          }

          final result = await InternetAddress.lookup(host)
              .timeout(const Duration(seconds: 5));

          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            hasInternet = true;
            if (kDebugMode) {
              print('NetworkMonitor: Successfully connected to $host');
            }
            break;
          }
        } catch (e) {
          if (kDebugMode) {
            print('NetworkMonitor: Failed to connect to $host: $e');
          }
          // Continue to next host
          continue;
        }
      }

      if (kDebugMode) {
        print('NetworkMonitor: Internet test result: $hasInternet');
      }

      if (hasInternet) {
        _handleConnectionRestored();
      } else {
        _handleNoConnection();
      }
    } catch (e) {
      if (kDebugMode) {
        print('NetworkMonitor: Error testing internet: $e');
      }
      _handleNoConnection();
    }
  }

  void _handleNoConnection() {
    if (!_isConnected) return; // Already handled

    if (kDebugMode) {
      print('NetworkMonitor: No connection detected');
    }

    _isConnected = false;

    if (!_isDialogShowing && _context != null) {
      if (kDebugMode) {
        print('NetworkMonitor: Showing network dialog');
      }
      _showNetworkDialog();
    }
  }

  void _handleConnectionRestored() {
    if (_isConnected) return; // Already connected

    if (kDebugMode) {
      print('NetworkMonitor: Connection restored');
    }

    _isConnected = true;

    if (_isDialogShowing && _context != null) {
      if (kDebugMode) {
        print('NetworkMonitor: Hiding network dialog due to connection restored');
      }
      _hideNetworkDialog();
    }
  }

  void _showNetworkDialog() {
    if (_context == null || _isDialogShowing) return;

    _isDialogShowing = true;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_context != null) {
        NetworkDialog.show(
          _context!,
          onRetry: () async {
            if (kDebugMode) {
              print('NetworkMonitor: Retry button pressed, testing connection...');
            }
            await _testInternetConnection();

            // If connection is restored, close dialog
            if (_isConnected) {
              if (kDebugMode) {
                print('NetworkMonitor: Connection restored, closing dialog');
              }
              _hideNetworkDialog();
            } else {
              if (kDebugMode) {
                print('NetworkMonitor: Still no connection');
              }
            }
          },
          onDismiss: () {
            if (kDebugMode) {
              print('NetworkMonitor: Dialog dismissed by user');
            }
            _isDialogShowing = false;
          },
        );
      }
    });
  }

  void _hideNetworkDialog() {
    if (!_isDialogShowing || _context == null) return;

    if (kDebugMode) {
      print('NetworkMonitor: Hiding network dialog');
    }

    _isDialogShowing = false;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_context != null) {
        try {
          // Try to pop the dialog
          if (Navigator.of(_context!).canPop()) {
            Navigator.of(_context!).pop();
            if (kDebugMode) {
              print('NetworkMonitor: Dialog closed successfully');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('NetworkMonitor: Error closing dialog: $e');
          }
        }
      }
    });
  }

  void dispose() {
    _connectivitySubscription?.cancel();
    _networkTestTimer?.cancel();
    _context = null;
  }
}
